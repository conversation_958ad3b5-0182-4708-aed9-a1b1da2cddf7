import { fetchDelete, fetchGet, fetchPost, fetchPostBlobFileAduits } from '../customFetch';

const classUrlPrefix = '/user/classification/forward';

export type ClassBuiltTempItem =  {
  tp_id: number;
  label_number: number;
  tp_name: string;
  subclass_count: number;
}

//模板管理-内置模板
export const getClassBuiltTempAPI = (params: any): Promise<ClassBuiltTempItem[]>  =>{
  return fetchPost(`${classUrlPrefix}/api/built_temp`, params);
}
//模板管理-新增模板
export const getClassNewTemplateAPI = (params: any): Promise<ClassBuiltTempItem[]> => {
  return fetchPost(`${classUrlPrefix}/api/template/manage`, params);
}

//模板管理-内置模板-查询标签
export const getClassBuiltTempTagList = (params: any): Promise<ClassBuiltTempItem[]> => {
  return fetchPost(`${classUrlPrefix}/api/query_tags_by_temp`, params);
}

//模板管理-模板-绑定标签
export const getClassBuiltTempBindTag = (params: any): Promise<ClassBuiltTempItem[]> => {
  return fetchPost(`${classUrlPrefix}/api/labels_bind`, params);
}

//模板管理-内置模板-新增分类
export const getClassAddCategory = (params: any): Promise<ClassBuiltTempItem[]> => {
  return fetchPost(`${classUrlPrefix}/api/add_category`, params);
}
//模板管理-内置模板-删除分类
export const getClassDelCategory = (params: any): Promise<ClassBuiltTempItem[]> => {
  return fetchPost(`${classUrlPrefix}/api/del_actegory`, params);
}

//模板管理-内置模板-删除分类
export const getClassEditCategory = (params: any): Promise<ClassBuiltTempItem[]> => {
  return fetchPost(`${classUrlPrefix}/api/edit_category`, params);
}

//模板管理-内置模板-绑定分级
export const getClassBindClsLevel = (params: any): Promise<ClassBuiltTempItem[]> => {
  return fetchPost(`${classUrlPrefix}/api/cls_bind`, params);
}


export type IClassIndustryItem = {
  name: string;
  type: number;
}
export type IClassIndustryParams  = {
  action: string;
  key: string;
  name?: string;
  type?: number;
}
export const  ClassStorageAPI = (params: IClassIndustryParams): Promise<IClassIndustryItem[]> =>{
  return fetchPost(`${classUrlPrefix}/api/storage`, params);
}

export type IClassTagParams = {
  action: string;
  label_name?: string;
  limit?: string;
  label_id?: number;
  type?: number;
  id?: number;
}

export type IClassTagItem = {

}
//模板管理 -行业类型

export const  ClassTagIndustryListAPI = (params: {limit: number}): Promise<IClassTagItem[]> =>{
  return fetchPost(`${classUrlPrefix}/api/industry_type_list`, params);
}

export const  ClassTagDeleteIndustryAPI = (params: {id: number}): Promise<IClassTagItem[]> =>{
  return fetchPost(`${classUrlPrefix}/api/industry_del`, params);
}

export const  ClassClassTagAPI = (params: IClassTagParams): Promise<IClassTagItem[]> =>{
  return fetchPost(`${classUrlPrefix}/api/classify_lev`, params);
}
//模板管理 -行业类型编辑
export interface IClassTagEditIndustryParams {
  name: string;
  details: string;
  id?: number;
  select_id: string;
  src_id: string
}
export const  ClassTagEditIndustry = (params: IClassTagEditIndustryParams): Promise<IClassTagItem[]> =>{
  return fetchPost(`${classUrlPrefix}/api/industry_edit`, params);
}
//模板管理 -行业类型- 新增
export const  ClassTagAddIndustry = (params: IClassTagEditIndustryParams): Promise<IClassTagItem[]> =>{
  return fetchPost(`${classUrlPrefix}/api/industry_add`, params);
}

interface IClassIndustryDatasourceParams {
  key: string
}
// 添加 行业 数据源
export const  getClassDatasourceAPI = (params: IClassIndustryDatasourceParams): Promise<any[]> =>{
  return fetchPost(`${classUrlPrefix}/api/query_storage`, params);
}

//标签管理 -导入
export const  ClassImportTagAPI = (params: any): Promise<any[]> =>{
  return fetchPost(`${classUrlPrefix}/db/classify_lev`, params);
}
//标签管理 -导入 -下载模板
export const  ClassTagDownloadExampleAPI = (params: any): Promise<any[]> =>{
  return fetchGet(`${classUrlPrefix}/download_example`, params);
}

//分类分级 -列表
export const  ClassTagTaskAPI = (): Promise<any[]> =>{
  return fetchGet(`${classUrlPrefix}/api/query_task_list`);
}

//分类分级 -删除
export const  ClassTagDeleteTaskAPI = (params: any): Promise<any[]> =>{
  return fetchPost(`${classUrlPrefix}/api/del_task`, params);
}

//分类分级 -新增
export const  ClassTaskAddTaskAPI = (params: any): Promise<any[]> =>{
  return fetchPost(`${classUrlPrefix}/api/add_task`, params);
}


//分类分级 -新增
export const  ClassTaskEditTaskAPI = (params: any): Promise<any[]> =>{
  return fetchPost(`${classUrlPrefix}/api/edit_task`, params);
}
//分类分级 -新增-关联数据源
export const  ClassTaskDatasourceAPI = (params: any): Promise<any[]> =>{
  return fetchGet(`${classUrlPrefix}/api/query_datasource`, params);
}

export interface IClassTaskExecuteTaskParams {
  classifys_id: number;
  id: number;
  recon_id: number
}
//分类分级 -执行任务
export const  ClassTaskExecuteTaskAPI = (params: IClassTaskExecuteTaskParams): Promise<any[]> =>{
  return fetchPost(`${classUrlPrefix}/api/exe_tasks`, params);
}

//结果-列结果
export const  ClassResColumnAPI = (params: IClassTaskExecuteTaskParams): Promise<any[]> =>{
  return fetchPost(`${classUrlPrefix}/api/query_column_grade`, params);
}
// //结果-表结果
export const  ClassResTableAPI = (params: IClassTaskExecuteTaskParams): Promise<any[]> =>{
  return fetchPost(`${classUrlPrefix}/api/query_table_grade`, params);
}

//结果-表结果 -列详情
export const  ClassResTableColumnDetailAPI = (params: IClassTaskExecuteTaskParams): Promise<any[]> =>{
  return fetchPost(`${classUrlPrefix}/api/query_table_columns`, params);
}

//结果-表结果 -定级详情
export const  ClassResTableGradeDetailAPI = (params: IClassTaskExecuteTaskParams): Promise<any[]> =>{
  return fetchPost(`${classUrlPrefix}/api/query_grade_details`, params);
}
//结果-表结果 -定级详情-人工定级
export const  ClassResTableSetManualGradeAPI = (params: {classify_id: number}): Promise<any[]> =>{
  return fetchPost(`${classUrlPrefix}/api/set_manual_grade`, params);
}