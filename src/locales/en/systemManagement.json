{"systemManagement.title": "System Management", "systemManagement.personManagement.title": "User Management", "systemManagement.personManagement.delete.success": "Delete Successful", "systemManagement.personManagement.group.delete.success": "Group Deletion Successful", "systemManagement.personManagement.dept": "Department", "systemManagement.personManagement.partner": "Partner", "systemManagement.personManagement.modal.delete.dept": "Are you sure you want to delete {{modalTitle}}: {{name}}?", "systemManagement.personManagement.modal.delete.group": "Are you sure you want to delete the group: {{name}}?", "systemManagement.personManagement.editName": "Edit Name", "systemManagement.personManagement.addEdpt": "Add Department", "systemManagement.personManagement.addGroup": "Add Group", "systemManagement.personManagement.editPrincipal": "Edit Person in Charge", "systemManagement.personManagement.export.tip1": "Task: Export user information for the selected department", "systemManagement.personManagement.export.tip2": "Execution completed, file generated successfully", "systemManagement.personManagement.btn.import": "Import Users", "systemManagement.personManagement.export.modal.title": "Are you sure you want to export user information for the currently selected department?", "systemManagement.personManagement.btn.export": "Export Users", "systemManagement.personManagement.user_parameter": "User Parameter Management", "systemManagement.personManagement.number_group": "Number of Users in Group", "systemManagement.personManagement.number_dept": "Number of Users in Department", "systemManagement.personManagement.number_company": "Number of Users in Company", "systemManagement.personManagement.setting.success": "Setting Successful", "systemManagement.personManagement.setting.error": "Setting Failed", "systemManagement.personManagement.twoFactor.no": "Some users have not set up two-factor authentication", "systemManagement.personManagement.reset_password.tip": "Are you sure you want to reset the password?", "systemManagement.personManagement.reset_password.success": "Password Reset Successful", "systemManagement.personManagement.table.column.user": "User", "systemManagement.personManagement.delete.tip": "Are you sure you want to delete the user?", "systemManagement.personManagement.delete.error": "Deletion Failed", "systemManagement.personManagement.batch_delete": "Batch Delete Users", "systemManagement.personManagement.batch_setting_time": "Batch Set Effective Time", "systemManagement.personManagement.alluUsers": "All Users", "systemManagement.personManagement.table.column.userName": "Username", "systemManagement.personManagement.table.column.userId": "<PERSON><PERSON> Account", "systemManagement.personManagement.table.column.departmentName": "Department", "systemManagement.personManagement.table.column.systemRoles": "User Roles", "systemManagement.personManagement.table.column.telephone": "Phone", "systemManagement.personManagement.table.column.createFrom": "CQ/Domain", "systemManagement.personManagement.table.column.auditRange": "Audit Range", "systemManagement.personManagement.table.column.ip": "Allowed Login IP", "systemManagement.personManagement.table.column.userStatus": "Status", "systemManagement.personManagement.table.column.loginAuth": "Login Authentication Method", "systemManagement.personManagement.table.column.twoFactor": "Two-Factor Authentication Method", "systemManagement.personManagement.table.column.effectTime": "Effective Time", "systemManagement.personManagement.createFrom.cqUser": "CQ User", "systemManagement.personManagement.createFrom.ad": "AD Domain", "systemManagement.personManagement.reset_otp.tip": "Are you sure you want to reset the OTP key?", "systemManagement.personManagement.btn.reset_otp": "Reset OTP", "systemManagement.personManagement.reset_user_password.tip": "Are you sure you want to reset this user's password?", "systemManagement.personManagement.delete_user.tip": "Are you sure you want to delete this user?", "systemManagement.personManagement.moveout.success": "Move Out Successful", "systemManagement.personManagement.search_user.placeholder": "Please enter username/account", "systemManagement.personManagement.btn.members": "Member Management", "systemManagement.personManagement.btn.users": "User Management", "systemManagement.personManagement.noPerm": "Your current role is [{{val}}], and you do not have permission to operate on [User Management]", "systemManagement.personManagement.btn.addUser": "Add User", "systemManagement.personManagement.table.pagination.total": "Total {{val}} records", "systemManagement.personManagement.addUser.ip.tip": "Example:", "systemManagement.personManagement.addUser.ip": "Allowed Login IP", "systemManagement.personManagement.btn.editUser": "Edit User Information", "systemManagement.personManagement.btn.editAndAdd": "Edit and Add User Information", "systemManagement.personManagement.addUser.parameter.hint": "Please enter {{val}}", "systemManagement.personManagement.addUser.dynamicFieldName": "Authentication Effective Time", "systemManagement.personManagement.addUser.userId.placeholder": "Please enter login account", "systemManagement.personManagement.addUser.password.placeholder": "Please enter a password longer than 6 characters", "systemManagement.personManagement.addUserModal.password": "Confirm Password", "systemManagement.personManagement.addUserModal.password.hint": "Please confirm the password", "systemManagement.personManagement.addUserModal.password.hint2": "The two passwords do not match", "systemManagement.personManagement.addUserModal.userName": "Username", "systemManagement.personManagement.addUserModal.userName.hint": "Please enter username", "systemManagement.personManagement.addUserModal.userGender": "User Gender", "systemManagement.personManagement.addUser.userGender.male": "Male", "systemManagement.personManagement.addUser.userGender.female": "Female", "systemManagement.personManagement.addUserModal.departmentId.hint": "Please select the department", "systemManagement.personManagement.addUserModal.jobNumber": "Job Number", "systemManagement.personManagement.addUserModal.jobNumber.hint": "Please fill in the job number", "systemManagement.personManagement.addUserModal.jobNumber.tip": "De<PERSON>ult is", "systemManagement.personManagement.addUserModal.role.disabled.tip": "There is currently no role management function, and editing user roles is not supported", "systemManagement.personManagement.addUserModal.telephone": "Mobile Number", "systemManagement.personManagement.addUserModal.telephone.tip": "Please enter mobile number", "systemManagement.personManagement.addUserModal.rangeType": "Audit Range", "systemManagement.personManagement.addUserModal.rangeType.tip": "Select the data range this auditor can view; by default, they can view operations of all users in the system.", "systemManagement.personManagement.addUserModal.rangeType.hint": "Please select audit range", "systemManagement.personManagement.addUserModal.rangeType.all": "Global Users", "systemManagement.personManagement.addUserModal.rangeType.custom": "Custom", "systemManagement.personManagement.addUserModal.auditRange": "Select Audit Range", "systemManagement.personManagement.addUserModal.phone.hint": "Invalid mobile number format", "systemManagement.personManagement.addUserModal.Phone.tip": "Please fill in the mobile number", "systemManagement.personManagement.addUserModal.userPassword": "User Password", "systemManagement.personManagement.addUserModal.ip": "Allowed Login IP", "systemManagement.personManagement.members.edit_success": "Member Edit Successful", "systemManagement.personManagement.members.groupMember": "Group Member", "systemManagement.personManagement.members.userId": "Account", "systemManagement.personManagement.batchEffectTime": "Batch Set Effective Time", "systemManagement.personManagement.batchEffectTime.tip": "Please select the authentication effective time!", "systemManagement.personManagement.parameter.search": "Enter Keywords", "systemManagement.personManagement.parameter.parameterName": "Parameter Name", "systemManagement.personManagement.parameter.tagName": "Parameter Tag", "systemManagement.personManagement.parameter.description": "Remarks", "systemManagement.personManagement.parameter.defaultValue": "Default Value", "systemManagement.personManagement.parameter.needed": "Required Field", "systemManagement.personManagement.parameter.dataSourceType": "Data Source", "systemManagement.personManagement.parameter.objectPath": "Object Path", "systemManagement.personManagement.parameter.objectName": "Object Name", "systemManagement.personManagement.parameter.filterRule": "Filter Rule", "systemManagement.personManagement.parameter.status": "Status", "systemManagement.personManagement.parameter.delete.tip": "Are you sure you want to delete: {{val}}?", "systemManagement.personManagement.parameter.add": "Add Parameter", "systemManagement.personManagement.parameter.filterRelevance": "View {{val}} Filter Relevance", "systemManagement.personManagement.parameter.params": "Parameters", "systemManagement.personManagement.parameter.action.tip": "{{val}} Successful", "systemManagement.personManagement.parameter.value": "Parameter Value {{val}}", "systemManagement.personManagement.parameter.parameterName.tip": "Please enter the parameter name", "systemManagement.personManagement.parameter.tagName.tip": "Please enter the parameter tag", "systemManagement.personManagement.parameter.examples": "Parameter Enumeration Values", "systemManagement.personManagement.parameter.needed.hint": "Required fields cannot be empty", "systemManagement.role": "Role Management", "systemManagement.role.save.tip": "Please select authorized permissions", "systemManagement.role.edit": "Edit Role", "systemManagement.role.edit.tip": "Confirm to delete this role?", "systemManagement.role.custom": "Custom Role", "systemManagement.role.new": "Add Role", "systemManagement.role.create": "Create Role", "systemManagement.role.create.roleName": "Role Name", "systemManagement.role.create.roleTemplateId": "Role Template", "systemManagement.role.create.description": "Role Description", "systemManagement.role.create.description.placeholder": "This is a role description, limited to 40 characters.", "systemManagement.role.roleName.tip": "Role name, limited to 20 characters.", "systemManagement.role.roleDesc": "Role Description:", "systemManagement.role.associatedUser": "Associated Users:", "systemManagement.role.authMode": "Authorization Mode:", "systemManagement.role.switchStatus.success": "Authorization mode switch toggled successfully", "systemManagement.role.bind.title": "Bind User", "systemManagement.role.bind.success": "Binding successful", "systemManagement.role.edit.success": "Edit successful", "systemManagement.role.bind.roleName": "Role Name:", "systemManagement.role.bind.boudendUser": "Boundend Users", "systemManagement.role.roleAuth": "Role Authorization", "systemManagement.role.status1": "All Permissions Not Authorized", "systemManagement.role.status2": "All Permissions Authorized", "systemManagement.role.status3": "All Permissions Required", "systemManagement.role.status4": "Read-Only Permissions Not Authorized", "systemManagement.role.status5": "Read-Only Permissions Authorized", "systemManagement.role.status6": "Readable Permissions Required", "systemManagement.role.edit.authDef": "Edit Permission Style Definition", "systemManagement.role.authDef": "Permission Style Definition", "systemManagement.system": "System Settings", "systemManagement.system.ui": "UI Configuration", "systemManagement.system.secretKey": "Application Key", "systemManagement.system.email": "<PERSON><PERSON>s", "systemManagement.system.sms": "SMS Gateway Settings", "systemManagement.system.sso": "Single Sign-On Configuration", "systemManagement.system.basic": "Basic Settings", "systemManagement.system.password": "Password Policy", "systemManagement.system.analysis": "Parsing Configuration", "systemManagement.system.auth": "Authorization Information", "systemManagement.system.watermark": "Watermark Settings", "systemManagement.system.access": "Access Settings", "systemManagement.system.dataMigration": "Data Migration", "systemManagement.system.alarm": "Alarm Configuration", "systemManagement.system.log": "Log Exposure Configuration", "systemManagement.system.ui.tip": "Only supports images in jpeg|png|jpg format", "systemManagement.system.ui.tip2": "Image must not exceed 200KB!", "systemManagement.system.ui.uploadSuccess": "Company logo uploaded successfully", "systemManagement.system.ui.delete.tip": "Are you sure you want to delete the company logo?", "systemManagement.system.ui.logoReplace": "Logo Replacement", "systemManagement.system.ui.companyLogo": "Company Logo", "systemManagement.system.ui.companyLogo.tip": "It is recommended to upload an image with a ratio of 11:4, in png, jpg, or jpeg format, and not exceeding 200KB", "systemManagement.system.ui.upload": "Upload Image", "systemManagement.system.ui.logoReplace.tip": "Logo will be updated on the next page refresh", "systemManagement.system.secretKey.table.createAt": "Creation Time", "systemManagement.system.secretKey.table.creator": "Creator", "systemManagement.system.secretKey.table.action.reset.tip": "Are you sure you want to reset this appSecret?", "systemManagement.system.secretKey.table.action.delete.tip": "Are you sure you want to delete the appSecret of {{appKey}}?", "systemManagement.system.secretKey.subTitle": "AppSecret Management", "systemManagement.system.secretKey.search.tip": "Please enter appKey", "systemManagement.system.secretKey.desc": "Remarks", "systemManagement.system.secretKey.desc.placeholder": "Please enter remarks", "systemManagement.system.secretKey.edit.title": "Edit AppSecret", "systemManagement.system.email.test": "Test Sending Email", "systemManagement.system.email.test.success": "Test Passed", "systemManagement.system.email.test.falled": "Test Failed", "systemManagement.system.email.host": "Email Server", "systemManagement.system.email.host.tip": "Please enter the email server", "systemManagement.system.email.port": "Email Server Port", "systemManagement.system.email.port.tip": "Please enter the email server port", "systemManagement.system.email.tls": "Use TLS Security Protocol", "systemManagement.system.email.emailAddress": "Sender <PERSON><PERSON>", "systemManagement.system.email.emailAddress.tip": "Please enter the sender email", "systemManagement.system.email.authCode": "Authorization Code", "systemManagement.system.email.authCode.tip": "Please enter the authorization code", "systemManagement.system.email.testEmailAddress": "Recipient Email", "systemManagement.system.email.testEmailAddress.tip": "Please enter the recipient email", "systemManagement.system.email.submit.tip": "Please test successfully before saving", "systemManagement.system.sms.commandReview": "Command Review", "systemManagement.system.sms.twoFactorAuth": "Two-Factor Authentication", "systemManagement.system.sms.alarmConfig": "Alarm Configuration", "systemManagement.system.sms.phone.overThreshold": "Exceeds Threshold", "systemManagement.system.sms.phone.check.empty": "There are empty variable values", "systemManagement.system.sms.phone.check.success": "Validation Successful", "systemManagement.system.sms.phone.check.falled": "Validation Failed", "systemManagement.system.sms.phone.submit.tip": "Please ensure SMS channel phone verification is successful before saving", "systemManagement.system.sms.phone.submit.tip2": "SMS channel configuration has changed, please send a test SMS again for verification", "systemManagement.system.sms.smsName": "Channel Name", "systemManagement.system.sms.smsName.tip": "Please fill in the channel name", "systemManagement.system.sms.smsName.hint": "Channel name cannot be empty", "systemManagement.system.sms.businessType": "Effective Alarm Type", "systemManagement.system.sms.businessType.hint": "Effective alarm type cannot be empty", "systemManagement.system.sms.businessType.tip": "Please select the effective alarm type", "systemManagement.system.sms.supplierType": "SMS Channel", "systemManagement.system.sms.supplierType.tip": "Please select an SMS channel", "systemManagement.system.sms.supplierType.hint": "AccessKeyID cannot be empty", "systemManagement.system.sms.accessKeySecret.hint": "AccessSecret cannot be empty", "systemManagement.system.sms.signName": "SMS Signature Name", "systemManagement.system.sms.signName.tip": "SMS signature name cannot be empty", "systemManagement.system.sms.templateCode": "SMS Template ID", "systemManagement.system.sms.templateCode.tip": "SMS Template ID cannot be empty", "systemManagement.system.sms.smsAppId.tip": "AppId cannot be empty", "systemManagement.system.sms.channelId": "Signature Channel Number", "systemManagement.system.sms.channelId.hint": "Signature channel number cannot be empty", "systemManagement.system.sms.param": "Parameter", "systemManagement.system.sms.param.state": "Signature channel number cannot be empty", "systemManagement.system.sms.alarmConfig.tip": "Please enter content {{val}}", "systemManagement.system.sms.content": "Message Content", "systemManagement.system.sms.content.hint": "Message content cannot be empty", "systemManagement.system.sms.content.extra": "Supported parameters for the current alarm type", "systemManagement.system.sms.baseUrl": "Request Address (URL)", "systemManagement.system.sms.baseUrl.hint": "Request address cannot be empty", "systemManagement.system.sms.method": "Request Type (Method)", "systemManagement.system.sms.method.hint": "Please select", "systemManagement.system.sms.urlParams": "API Parameters", "systemManagement.system.sms.body": "Body Parameters", "systemManagement.system.sms.headers": "HTTP Headers", "systemManagement.system.sms.phone": "Recipient Phone Number", "systemManagement.system.sms.phone.hint": "Please enter the phone number!", "systemManagement.system.sms.testPhone": "Send Test SMS", "systemManagement.system.sms.templateName": "SMS Channel Template Name", "systemManagement.system.sms.createTemplate": "Create New SMS Channel Template", "systemManagement.system.sms.editTemplate": "Edit SMS Channel Template", "systemManagement.system.sms.copyTemplate": "Copy SMS Channel Template", "systemManagement.system.sms.smsTemplate.step1": "Fill in Basic Information for SMS Channel", "systemManagement.system.sms.smsTemplate.step2": "Verify Channel Availability", "systemManagement.system.sms.smsTemplate.step3": "Complete", "systemManagement.system.sms.smsTemplate.back": "Return to Alarm Configuration", "systemManagement.system.sms.smsTemplate.backTitle": "Returning to alarm configuration in {{second}}s", "systemManagement.system.sms.smsTemplate.backSuccess": "Submission Successful", "systemManagement.system.sso.ad": "AD Domain Configuration", "systemManagement.system.sso.openLdap": "OpenLdap Configuration", "systemManagement.system.oauth.openLdap": "CAS Configuration", "systemManagement.system.oauth.oauth": "Oauth2.0 Configuration", "systemManagement.system.radius": "<PERSON><PERSON> Configuration", "systemManagement.system.openLdap.hint1": "Please enter the login address", "systemManagement.system.openLdap.hint2": "OpenLdap login address must start with ldap(s):// or LDAP(s)://", "systemManagement.system.openLdap.openLdapSwitch": "Enable OpenLdap", "systemManagement.system.openLdap.openLdapAdminName": "Ad<PERSON> Account", "systemManagement.system.openLdap.adminSecret": "Admin Password", "systemManagement.system.openLdap.openLdapUrl": "Login Address", "systemManagement.system.openLdap.searchScope": "<PERSON> Scope", "systemManagement.system.openLdap.searchFilter": "User Login Attribute", "systemManagement.system.openLdap.customFormItem.hint": "Please enter custom attribute", "systemManagement.system.ad.url.hint": "Please enter the URL", "systemManagement.system.ad.url.hint2": "Must start with ldap:// or LDAP://", "systemManagement.system.ad.suffix.hint": "Must start with @", "systemManagement.system.ad.suffix.hint2": "Character length cannot exceed 30", "systemManagement.system.ad.adDirectLdapAddress": "AD Domain Address", "systemManagement.system.ad.openLdapUrl": "Login Address", "systemManagement.system.ad.searchScope": "<PERSON> Scope", "systemManagement.system.ad.adDirectSearchBase": "Search Directory", "systemManagement.system.ad.adDirectSearchBase.hint": "Please enter the search directory", "systemManagement.system.ad.adDirectSearchBase.tip": "OU=Test Group,DC=liu,DC=com", "systemManagement.system.ad.adDirectUsernameSuffix": "Login User Name Suffix", "systemManagement.system.ad.isCreateAccount": "Automatically Create Account with Same Name", "systemManagement.system.cas.url.hint": "Must start with http:// or https://", "systemManagement.system.cas.casServerValidateUrl": "Validation URL", "systemManagement.system.cas.casServerLogoutUrl": "Logout URL", "systemManagement.system.cas.casCQServerName": "Service Deployment Address", "systemManagement.system.cas.casUserNameSuffix": "Login User Name Suffix", "systemManagement.system.oauth.oauth2IsStandard": "Version Type", "systemManagement.system.oauth.oauth2IsStandard.tip": "Standard Version: The authentication center uses the official standard interface, logic implemented in Java. Client Version: Due to custom development in the client's authentication center, authorization and authentication processes need to be implemented in Python, requiring customization to retrieve user information.", "systemManagement.system.oauth.oauth2IsStandard.coem": "Standard Version", "systemManagement.system.oauth.oauth2IsStandard.client": "Client Version", "systemManagement.system.oauth.userAuthorizationUrl": "Authentication Center Authorize URL", "systemManagement.system.oauth.accessTokenUrl": "Authentication Center Token URL", "systemManagement.system.oauth.oauth2CQServerUrl": "Service Deployment Address", "systemManagement.system.oauth.clientId": "Authentication Center Authorized Client ID", "systemManagement.system.oauth.clientSecret": "Authentication Center Authorized Client Secret", "systemManagement.system.oauth.resourceUserInfoUrl": "User Information OpenAPI URL", "systemManagement.system.oauth.resourceUserInfoParma": "User Information OpenAPI Field Name", "systemManagement.system.oauth.logoutUrl": "Logout URL", "systemManagement.system.raduis.logoutUrl": "Radius Server IP", "systemManagement.system.raduis.logoutUrl.tip": "Please enter the Radius Server IP", "systemManagement.system.raduis.radiusPort": "Radius Server Port", "systemManagement.system.raduis.radiusPort.tip": "Please enter the Radius Server Port", "systemManagement.system.raduis.radiusPort.hint": "Please enter a valid port number", "systemManagement.system.raduis.sharedSecret": "Shared Secret", "systemManagement.system.raduis.sharedSecret.tip": "Please enter the Shared Secret", "systemManagement.system.other.sdtMenu": "Right <PERSON><PERSON>", "systemManagement.system.other.resultSetOperation": "Result Set Operation", "systemManagement.system.other.exportFunction": "Export Function", "systemManagement.system.other.forceOtpBing": "Please turn off forced SMS authentication login before disabling forced phone binding!", "systemManagement.system.other.forcePhoneBing": "Please turn off forced OTP authentication login before disabling forced OTP binding!", "systemManagement.system.other.flag.true": "Global Setting", "systemManagement.system.other.flag.false": "User Customization", "systemManagement.system.other.smsLogin": "SMS Authentication", "systemManagement.system.other.otpLogin": "OTP Authentication", "systemManagement.system.other.loginSetting.tip": "Are you sure you want to set all users' login methods to {{val}} login?", "systemManagement.system.other.loginSetting.content": "After setting, all users' login settings in the platform will change to '{{val}}'", "systemManagement.system.other.loginTime": "Login Retention Time", "systemManagement.system.other.loginTime.tip": "Please enter the login retention time", "systemManagement.system.other.loginTime.hint": "Please enter a number between 1-180", "systemManagement.system.other.loginIpImpose.tip": "You can enter a maximum of 10 user IPs", "systemManagement.system.other.debugUrl.hint": "Please enter the public or private address", "systemManagement.system.other.otp.hint1": "Please ensure forced OTP binding is enabled", "systemManagement.system.other.otp.hint2": "Please ensure forced phone binding is enabled", "systemManagement.system.other.subTitle": "Basic Settings", "systemManagement.system.other.importNumber": "Import Task Limit", "systemManagement.system.other.importNumber.hint": "Please enter a value from 1 and 30", "systemManagement.system.other.importNumber.tip": "Please enter a value", "systemManagement.system.other.exportNumber": "Export Task Limit", "systemManagement.system.other.batchExec": "Batch Execution Task Limit", "systemManagement.system.other.readSize": "File Read Limit (MB)", "systemManagement.system.other.readSize.hint": "Please enter a value between 1 and 100", "systemManagement.system.other.uploadSize": "File Upload Limit (MB)", "systemManagement.system.other.limit": "Export File Download Limit (times)", "systemManagement.system.other.limit.extra": "0 means no downloads allowed", "systemManagement.system.other.limit.hint": "Please enter a value between 0 and 10000", "systemManagement.system.other.limit.tip": "Please enter the download limit count", "systemManagement.system.other.accountLockPeriod": "Account Lock Period (days)", "systemManagement.system.other.accountLockPeriod.extra": "Accounts that have not logged in within the set period will be locked", "systemManagement.system.other.days": "Data Backup File Validity Settings", "systemManagement.system.other.days.extra": "Automatically destroy data protection files that exceed the validity period", "systemManagement.system.other.days.tip": "Please select the validity period for data backup files", "systemManagement.system.other.basic.forceOtpBing": "Forced OTP Binding", "systemManagement.system.other.basic.forceOtpBing.extra": "Enabling this will force all users in the system to bind OTP", "systemManagement.system.other.basic.forcePhoneBing": "Forced Phone Binding", "systemManagement.system.other.basic.forcePhoneBing.tip": "The prerequisite for enabling this is that there is an available SMS channel in the SMS gateway settings; enabling this will force all users in the system to bind their phones", "systemManagement.system.other.multipleDeviceLogin": "Multiple Device Login", "systemManagement.system.other.multipleDeviceLogin.tip": "Enabling this allows multiple device logins for the same account within the same time period", "systemManagement.system.other.setting": "Display All Resources on Access Application Page", "systemManagement.system.other.setting.tip": "When disabled, the access application page will only display matching resources by entering IP", "systemManagement.system.other.resultEnc": "Result Set Export Encryption", "systemManagement.system.other.resultEnc.tip": "Enabling this will encrypt PDF and EXCEL files exported from the result set", "systemManagement.system.other.loginSetting": "System User Login Settings", "systemManagement.system.other.globalEffectTime": "Two-Factor Authentication Effective Time", "systemManagement.system.other.loginRetentionTimeType": "System User Login Retention Time Settings", "systemManagement.system.other.sysFilterLib": "System Filter Library", "systemManagement.system.other.filterResources.tip": "Please select a data source", "systemManagement.system.other.debugUrl": "Debug Address", "systemManagement.system.other.publicDebugUrl": "Public Address", "systemManagement.system.other.privateDebugUrl": "Private Address", "systemManagement.system.other.toolPermission": "Tool Permission Settings", "systemManagement.system.other.resultSetTipOption": "No Permission Tip Entry Settings", "systemManagement.system.other.resultSetTipOption.extra": "The selected entry will be displayed when there is no permission to operate", "systemManagement.system.other.resultSetTipOption.tip": "Please select a no permission tip entry", "systemManagement.system.other.cascadeRemoval.label": "Cascade Removal Settings", "systemManagement.system.other.connectionFailedCount": "Connection Failed Freeze Count", "systemManagement.system.other.connectionFailedCount.extra": "0 means no limit", "systemManagement.system.other.connectionFailedCount.tip": "Please enter the freeze count", "systemManagement.system.ps.maxDay": "User Password Validity Period (Days)", "systemManagement.system.ps.maxDay.extra": "It is recommended that all users reset their passwords once before modifying this configuration (days)", "systemManagement.system.ps.maxDay.tip": "It is recommended to reset the password when modifying 'User Password Validity Period (Days)'. If already reset, please ignore!", "systemManagement.system.ps.passwordMin": "Minimum Length", "systemManagement.system.ps.passwordMin.hint": "Please enter a value greater than 0, and this value must be above the lower limit", "systemManagement.system.ps.passwordMin.hint2": "Please enter an integer between the lower limit and 20", "systemManagement.system.ps.passwordMax": "Maximum Length", "systemManagement.system.ps.min": "Lower Limit", "systemManagement.system.ps.pwdInclude": "Password Must Include", "systemManagement.system.ps.maxDay.hint": "Please enter a valid value of 1 or above", "systemManagement.system.ps.maxDay.tip2": "Please enter the password validity period", "systemManagement.system.ps.noRepeatCount": "User Password Repeat Cycle (Times)", "systemManagement.system.ps.noRepeatCount.extra": "The same password cannot be repeated within the set cycle", "systemManagement.system.ps.noRepeatCount.hint": "Please enter a valid value between 1-10", "systemManagement.system.ps.noRepeatCount.tip": "Please enter the user password repeat cycle count", "systemManagement.system.ps.expireWarning": "Enable Password Expiration Warning", "systemManagement.system.ps.expireWarning.extra": "If the switch is off, no password expiration messages will be shown", "systemManagement.system.ps.expireWarningDay": "Password Expiration Warning Message (Days)", "systemManagement.system.ps.expireWarningDay.tip": "Please enter the number of days for the password expiration warning", "systemManagement.system.ps.expireWarningDay.hint": "Please enter a valid value between 1-15", "systemManagement.system.ps.maxFailureCount": "Account Lock Input Count", "systemManagement.system.ps.maxFailureCount.hint": "Please enter a valid value of 1 or above", "systemManagement.system.ps.maxFailureCount.tip": "Please enter the account lock input count", "systemManagement.system.ps.lockoutMinute": "Account Lock Duration (Minutes)", "systemManagement.system.ps.lockoutMinute.hint": "Please enter a valid value between 1-999999", "systemManagement.system.ps.lockoutMinute.tip": "Please enter the account lock duration", "systemManagement.system.ps.userPwdStrong": "User Password Strength", "systemManagement.system.ps.userPwdStrong.tip2": "Built-in password strength in the system", "systemManagement.system.ps.userPwdStrong.tip": "Password length should be between 9-16 characters, must include numbers, uppercase letters, lowercase letters, and special characters", "systemManagement.system.ps.defaultPassword": "New User Password", "systemManagement.system.parsing.baseline": "Loose Interception Mode", "systemManagement.system.parsing.baseline.tip": "Select Data Source", "systemManagement.system.parsing.export.tip": "Task Parsing Failure Export", "systemManagement.system.parsing.export.tip2": "Execution completed, file generated successfully", "systemManagement.system.parsing.search": "Search SQL Statement", "systemManagement.system.parsing.fall": "Parsing Failed Statement", "systemManagement.system.parsing.sql": "SQL Statement", "systemManagement.system.parsing.table.sql": "Parsing Failed Statement", "systemManagement.system.parsing.table.connectionName": "Connection Name", "systemManagement.system.parsing.table.databaseType": "Data Source Type", "systemManagement.system.parsing.table.resultStatus": "Execution Status", "systemManagement.system.parsing.table.parsingFailedMessage": "Failure Reason", "systemManagement.system.parsing.table.executeType": "Operation Type", "systemManagement.system.parsing.table.userId": "Operating User", "systemManagement.system.parsing.table.startTime": "Execution Time", "systemManagement.system.auth.deleteLicense.success": "License Deletion Successful", "systemManagement.system.auth.deleteLicense.error": "License Deletion Failed", "systemManagement.system.auth.updateLicense.error": "License Update Failed", "systemManagement.system.auth.updateLicense.loading": "Updating", "systemManagement.system.auth.license.uninstalled": "License service not installed, unable to download", "systemManagement.system.auth.fileType.tip": "Please select a file of type ${{lisenceFileType}}", "systemManagement.system.auth.startTime": "Authorization Start Time", "systemManagement.system.auth.endTime": "Authorization End Time", "systemManagement.system.auth.databaseNumber": "Database Managed Quantity Limit", "systemManagement.system.auth.connectionNumber": "Managed Database Quantity", "systemManagement.system.auth.databaseType": "Supported Database Types", "systemManagement.system.auth.productGrade": "Product Model", "systemManagement.system.auth.licenseDownload": "License Download", "systemManagement.system.auth.licenseDownload.extra": "Ensure that the license service is installed", "systemManagement.system.auth.updateLicense": "License Update", "systemManagement.system.auth.newLicense": "Upload New License", "systemManagement.system.auth.updates": "One-Click Update", "systemManagement.system.auth.notUpdate": "No New License Uploaded", "systemManagement.system.auth.toBeUpdated": "Pending Update", "systemManagement.system.auth.updatedSuccess.tip": "License Update Completed!", "systemManagement.system.watermark.updated": "Watermark Effect Saved Successfully", "systemManagement.system.watermark.result": "Watermark Effect:", "systemManagement.system.watermark.color": "Watermark Color:", "systemManagement.system.watermark.tip": "Watermark content is too long; font size settings may not take effect", "systemManagement.system.watermark.fontSize": "Watermark Font Size:", "systemManagement.system.watermark.spacing": "Watermark Spacing:", "systemManagement.system.watermark.crosswise": "Horizontal:", "systemManagement.system.watermark.vertical": "Vertical:", "systemManagement.system.watermark.watermarkName": "System Watermark Settings", "systemManagement.system.watermark.watermarkName.tip": "Please enter $ for quick configuration", "systemManagement.system.watermark.pdfWatermarkName": "PDF Watermark Settings", "systemManagement.system.watermark.pdfWatermarkName.tip": "Supports selecting up to three parameters", "systemManagement.system.watermark.pdfWatermarkName.hint": "Select up to three parameters", "systemManagement.system.watermark.excelWatermarkName": "EXCEL Watermark Settings", "systemManagement.system.watermark.preview": "Preview:", "systemManagement.system.data.upload.tip": "Only JSON format files are supported", "systemManagement.system.data.upload.success": "File uploaded successfully!", "systemManagement.system.data.tip": "You can upload data exported from other machines to migrate it to this machine", "systemManagement.system.data.upload.tip2": "The import mode is incremental import, which will not overwrite existing data. Please ensure that there is no historical data on the current machine to avoid incomplete data import", "systemManagement.system.data.log": "Log:", "systemManagement.system.data.clear": "Clear Log", "systemManagement.system.data.download": "Download Log", "systemManagement.system.access.export.tip": "Task: Export selected access rules", "systemManagement.system.access.delete.tip": "This IP is currently active. Are you sure you want to delete?", "systemManagement.system.access.table.ipAddr": "Source", "systemManagement.system.access.table.type": "Access Policy", "systemManagement.system.access.table.type1": "Whitelist Policy", "systemManagement.system.access.table.type2": "Blacklist Policy", "systemManagement.system.access.table.userIds": "Bound Users", "systemManagement.system.access.batchExport.tip": "Are you sure you want to export the currently selected access rules?", "systemManagement.system.access.batchDelete.tip": "Are you sure you want to delete the currently selected access rules?", "systemManagement.system.access.batchExport": "Batch Export Access Rules", "systemManagement.system.access.batchDelete": "Batch Delete Access Rules", "systemManagement.system.access.search": "Search Source", "systemManagement.system.access.exportRule": "Import Access Rules", "systemManagement.system.access.addRule": "Add Access Rule", "systemManagement.system.access.add.tip": "This IP is currently using CloudQuery. Are you sure you want to proceed?", "systemManagement.system.access.add.title": "Add Access Rule", "systemManagement.system.access.edit.title": "Edit Access Rule", "systemManagement.system.access.add.ipAddr": "Source", "systemManagement.system.access.add.ipAddr.extra": "Source supports the following formats: Single IP: ***********  CIDR: ***********/25", "systemManagement.system.access.add.ipAddr.plac": "Input cannot be empty", "systemManagement.system.access.add.ipAddr.hint": "Input is invalid, please recheck", "systemManagement.system.access.add.type": "Access Policy", "systemManagement.system.access.add.userIds.extra": "After selecting users for binding, this user can only log in successfully from this IP", "systemManagement.system.access.add.userIds": "Bound Users", "systemManagement.system.access.batchImport": "Batch Import Access Rules", "systemManagement.system.alarm.sysTemAlarmConfig": "System Alarm Configuration", "systemManagement.system.alarm.businessAlarmConfig": "Business Alarm Configuration", "systemManagement.system.alarm.sysTemAlarmConfig.subTitle": "System Alarm Type", "systemManagement.system.alarm.businessAlarmConfig.subTitle": "Business Alarm Type", "systemManagement.system.alarm.condition": "Alarm Condition", "systemManagement.system.alarm.methodDetail": "Alarm Method", "systemManagement.system.alarm.targetPerson": "Message Recipient Type", "systemManagement.system.alarm.userNameWithIds": "Message Recipients", "systemManagement.system.alarm.positiveNum": "<PERSON><PERSON><PERSON><PERSON>(s)", "systemManagement.system.alarm.positiveNumMB": "<PERSON><PERSON><PERSON><PERSON>(MB）", "systemManagement.system.alarm.positiveNum.plac": "Please enter the threshold", "systemManagement.system.alarm.positiveNum.hint": "Threshold cannot be less than 1", "systemManagement.system.alarm.positiveNum.hint2": "Threshold cannot be greater than 20", "systemManagement.system.alarm.checkLevel": "Review Level", "systemManagement.system.alarm.edit.SysTemAlarmConfig": "Edit System Alarm Configuration", "systemManagement.system.alarm.edit.usinessAlarmConfig": "Edit Business Alarm Configuration", "systemManagement.system.alarm.alarmMethods": "Alarm Methods", "systemManagement.system.alarm.alarmMethods.plac": "Please select an alarm method", "systemManagement.system.log.syslog": "Expose via syslog", "systemManagement.system.log.kafka": "Expose via Kafka", "systemManagement.system.log.host": "Syslog Server IP Address", "systemManagement.system.log.host.plac": "Please enter the syslog server IP address", "systemManagement.system.log.port": "Syslog Server Port Number", "systemManagement.system.log.port.plac": "Please enter the syslog server port number", "systemManagement.system.log.protocol": "Syslog Server Protocol", "systemManagement.system.log.protocol.plac": "Please select the syslog server protocol", "systemManagement.system.log.level": "Log Level", "systemManagement.system.log.level.plac": "Please select the log level", "systemManagement.system.log.kafka.host": "Kafka Server Address", "systemManagement.system.log.kafka.host.plac": "Please enter the Kafka server address", "systemManagement.system.log.kafka.topic": "Kafka Topic Name", "systemManagement.system.log.kafka.topic.plac": "Please enter the Kafka topic name", "systemManagement.system.log.kafka.clientId": "Kafka Client ID", "systemManagement.system.log.kafka.clientId.plac": "Please enter the Kafka client ID", "systemManagement.basic.validPeriod": {"oneMonth": "One Month", "threeMonth": "Three Months", "halfYear": "Six Months", "year": "One Year", "forever": "forever"}, "systemManagement.system.targetPerson.PERSONAL": "Personal", "systemManagement.system.targetPerson.DEPT": "Department Head", "systemManagement.system.targetPerson.CONN": "Connection Administrator", "systemManagement.system.targetPerson.CPU_USAGE": "CPU Usage", "systemManagement.system.targetPerson.MEMORY_USAGE": "Memory Usage", "systemManagement.system.targetPerson.UPLOAD_FILE_SIZE": "File Upload Size", "systemManagement.system.targetPerson.IMPORT_TASK_COUNT": "Import Task Count", "systemManagement.system.targetPerson.EXPORT_TASK_COUNT": "Export Task Count", "systemManagement.system.targetPerson.LICENSE_REMAINING": "License Remaining Days", "systemManagement.system.targetPerson.OVER_PERMISSION": "Over Permission Operation", "systemManagement.system.targetPerson.HIGH_RISK": "High-Risk Operation", "systemManagement.system.targetPerson.SLOW_SQL": "Slow SQL", "systemManagement.system.targetPerson.BATCH_EXECUTE": "Batch Execute", "systemManagement.system.targetPerson.SQL_CHECK": "SQL Review", "systemManagement.system.targetPerson.PROBLEM_CONNECTION": "Problem Connection", "systemManagement.system.targetPerson.SYSTEM_ERROR": "System Error", "systemManagement.personManagement.editCompanyName": "Edit Company Name", "systemManagement.personManagement.companyName": "Company Name", "systemManagement.personManagement.companyName.plac": "Please enter the company name", "systemManagement.personManagement.addDept.success": "Department added successfully", "systemManagement.personManagement.addDept.subordinateCompany": "Affiliated Company", "systemManagement.personManagement.deptName": "Department Name", "systemManagement.personManagement.deptName.plac": "Please enter the department name", "systemManagement.personManagement.deptName.hint": "Department name must be between 2 and 20 characters", "systemManagement.personManagement.deptName.hint2": "Can only consist of Chinese characters, numbers, and letters", "systemManagement.personManagement.deptType": "Department Type", "systemManagement.personManagement.outsideParties": "External Departments", "systemManagement.personManagement.editDeptName": "Edit Department Name", "systemManagement.personManagement.editPartnerName": "Edit Partner Name", "systemManagement.personManagement.partnerName": "Partner Name", "systemManagement.personManagement.name.hint": "Cannot exceed 100 characters", "systemManagement.personManagement.addGroup.success": "Group added successfully", "systemManagement.personManagement.groupName": "Group Name", "systemManagement.personManagement.groupName.plac": "Please enter the group name", "systemManagement.personManagement.groupName.hint": "Group name must be between 2 and 100 characters", "systemManagement.personManagement.desc": "Description and Remarks", "systemManagement.personManagement.editGroupHead": "Edit Department Head", "systemManagement.personManagement.editPartnerHead": "Edit Partner Head", "systemManagement.personManagement.principal": "Person in Charge", "systemManagement.personManagement.principal.plac": "Please enter the person in charge", "systemManagement.personManagement.continue.sync.error": "Continue Sync on <PERSON><PERSON><PERSON>", "systemManagement.system.other.sysFunctionSetting": "System Functionality Limitation Settings", "systemManagement.system.other.sysLoginSetting": "System Login Settings", "systemManagement.system.other.sysFunctionModuleSetting": "System Function Module Settings", "systemManagement.system.other.OtherSetting": "Other Settings", "systemManagement.system.alarm.silentCycle.cupTip": "The system checks the CPU utilization rate once every cycle. If the trigger condition is met, an alarm will be triggered.", "systemManagement.system.alarm.silentCycle.memoryTip": "The system checks the memory utilization rate once every cycle. If the trigger condition is met, an alarm will be triggered.", "systemManagement.system.alarm.silentCycle.sysErrorTip": "The system checks the system once every cycle. If the trigger condition is met, an alarm will be triggered.", "systemManagement.system.alarm.trigger.cpuTip": "CPU utilization rate > threshold, and lasts for a period of time", "systemManagement.system.alarm.trigger.memoryTip": "Memory utilization rate > threshold, and lasts for a period of time", "systemManagement.system.alarm.trigger.OtherTip": "Trigger alarm immediately or accumulative trigger, where trigger alarm immediately means alarm is triggered immediately upon triggering, and accumulative trigger means alarm is triggered when the number of triggers within a certain time period reaches a certain threshold", "systemManagement.system.alarm.trigger": "Alarm Trigger Condition", "systemManagement.system.alarm.duration": "Duration(min)", "systemManagement.system.alarm.duration.tip": "Please enter an integer between 1-720", "systemManagement.system.alarm.interval": "Silent Cycle(min)", "systemManagement.system.alarm.policyType": "Trigger Condition", "systemManagement.system.alarm.policyType.value0": "Trigger Alarm Immediately", "systemManagement.system.alarm.policyType.value1": "Accumulative Trigger", "systemManagement.system.alarm.policyType.label": "Accumulative Trigger Policy", "systemManagement.system.alarm.policyType.label.extra0": "within", "systemManagement.system.alarm.policyType.label.extra1": "min, accumulative trigger", "systemManagement.system.alarm.policyType.label.extra2": "immediate alarm", "systemManagement.system.alarm.policyType.interval": "Please enter an integer between 1-720 as the trigger time period", "systemManagement.system.alarm.policyType.count": "Please enter an integer between 1-720 as the accumulative trigger count", "systemManagement.system.alarm.value%": "<PERSON><PERSON><PERSON><PERSON>(%)", "systemManagement.system.alarm.valueDay": "<PERSON><PERSON><PERSON><PERSON>(Days)", "systemManagement.system.alarm.valueItem": "<PERSON><PERSON><PERSON><PERSON>(Items)", "systemManagement.system.other.autoDownload": "Whether to automatically download when exporting files", "systemManagement.system.targetPerson.FILE_EXPORT_SIZE": "File Export Size", "systemManagement.system.alarm.processMode.label": "Process Mode", "systemManagement.system.alarm.processMode.value1": "Abort task and alert", "systemManagement.system.alarm.processMode.value2": "<PERSON><PERSON> only", "systemManagement.system.alarm.positiveNumGB": "<PERSON><PERSON><PERSON><PERSON>(GB）", "systemManagement.system.ps.passwordMin.plac": "Please enter an integer between 1 and 19", "systemManagement.system.authMenu.title": "Permission Settings", "systemManagement.system.authMenu.name.hint": "The name already exists.", "systemManagement.system.authMenu.name.label": "Name", "systemManagement.system.authMenu.name.plac": "Please enter the name", "systemManagement.system.authMenu.name.hint2": "Can only consist of Chinese characters, numbers, and English letters, 2-24 characters", "systemManagement.system.authMenu.parameter.label": "Parameter Value", "systemManagement.system.authMenu.parameter.hint": "Please complete the parameter value", "systemManagement.system.authMenu.parameter.hint2": "Only integers ranging from 1 to 9999999 can be input", "systemManagement.system.authMenu.status.label": "Status", "systemManagement.system.authMenu.parameter.day": "Day", "systemManagement.system.authMenu.parameter.hour": "Hour", "systemManagement.system.authMenu.customAuthPeriod.label": "Custom Permission Duration", "systemManagement.system.authMenu.customAuthPeriod.up": "Move Up", "systemManagement.system.authMenu.customAuthPeriod.down": "Move Down", "systemManagement.system.authMenu.customAuthPeriod.moveSuccessful": "Move Successful", "systemManagement.system.authMenu.customAuthPeriod.custom": "Custom"}