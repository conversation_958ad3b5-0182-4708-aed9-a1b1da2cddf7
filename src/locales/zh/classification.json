{"classGrading.title": "数据分类分级", "classGrading.tab.template.title": "模板管理", "classGrading.tab.tag.title": "标签管理", "classGrading.tab.task.title": "分类分级任务", "classGrading.tab.result.title": "分级结果", "classGrading.tab.template.tab.builtIn": "内置模板", "classGrading.tab.template.tab.new": "新增模板", "classGrading.tab.template.action.addClass": "新增分类", "classGrading.tab.template.action.addTemplate": "新增模板", "classGrading.tab.template.action.gradeConfig": "分级配置", "classGrading.tab.template.action.importTemplate": "导入模板", "classGrading.tab.task.timeCycle.MANUAL": "手动", "classGrading.tab.task.timeCycle.DAY": "每天", "classGrading.tab.task.timeCycle.TWO_DAY": "每两天", "classGrading.tab.task.timeCycle.THREE_DAY": "每三天", "classGrading.tab.task.timeCycle.WEEK": "每周", "classGrading.tab.task.timeCycle.TWO_WEEK": "每两周", "classGrading.tab.task.timeCycle.THREE_WEEK": "每三周", "classGrading.tab.task.status.notStarted": "未开始", "classGrading.tab.task.status.pending": "等待中", "classGrading.tab.task.status.success": "执行成功", "classGrading.tab.task.status.failed": "执行失败", "classGrading.tab.result.res.all": "全部", "classGrading.tab.result.res.grade": "已分级", "classGrading.tab.result.res.unGrade": "未分级", "classGrading.tab.result.tab.column": "列结果", "classGrading.tab.result.tab.table": "表结果", "classGrading.tab.result.column.columnName": "列名", "classGrading.tab.result.column.columnDesc": "列备注", "classGrading.tab.result.column.tag": "所属标签", "classGrading.tab.result.column.class": "所属分类", "classGrading.tab.result.column.model": "模型分类", "classGrading.tab.result.column.grade": "所属分级", "classGrading.tab.result.column.table": "所属表", "classGrading.tab.result.column.tableDesc": "表备注", "classGrading.tab.result.column.schema": "所属schema", "classGrading.tab.result.column.database": "所属数据库", "classGrading.tab.result.column.connection": "所属连接", "classGrading.tab.result.column.connectionType": "连接类型", "classGrading.tab.result.column.address": "连接地址", "classGrading.tab.result.column.classTask": "所属分类分级任务", "classGrading.tab.result.column.searchPlac": "请输入列名/列备注/表名/表备注/所属分类", "classGrading.tab.result.column.searchTask.plac": "请选择分类分级任务", "classGrading.tab.result.column.view": "定级详情", "classGrading.tab.result.column.tableName": "表名", "classGrading.tab.result.column.radio": "分类占比", "classGrading.tab.result.column.count": "分级数量", "classGrading.tab.result.column.level": "所属等级", "classGrading.tab.result.column.isOverThreshold": "是否超过阈值", "classGrading.tab.result.column.isManual": "是否人工定级", "classGrading.tab.result.column.manual": "设为人工定级", "classGrading.tab.result.columnInfo": "列信息", "classGrading.tab.result.column.className": "分类名称", "classGrading.tab.result.column.manualClassName": "人工定级分类", "classGrading.tab.result.column.classCount": "分类数量", "classGrading.tab.result.column.totalColumnCount": "总列数", "classGrading.tab.result.column.classPercentage": "分级占比", "classGrading.tab.result.column.tableName1": "表名称", "classGrading.tab.result.tableSearch.plac": "请输入表名称/备注/所属分类", "classGrading.tab.task.column.taskName": "任务名称", "classGrading.tab.task.column.dataSource": "关联数据源", "classGrading.tab.task.column.executeType": "执行类型", "classGrading.tab.task.column.executeStrategy": "执行策略", "classGrading.tab.task.column.scheduleCycle": "调度周期", "classGrading.tab.task.column.industryType": "关联行业类型", "classGrading.tab.task.column.template": "分类分级模板", "classGrading.tab.task.column.executeTask": "执行任务", "classGrading.tab.task.column.recognitionResult": "识别结果", "classGrading.tab.task.editTask": "编辑分类分级任务", "classGrading.tab.task.addTask": "新增分类分级任务", "classGrading.tab.task.taskName.plac": "请输入任务名称", "classGrading.tab.task.dataSource.plac": "请选择关联数据源", "classGrading.tab.task.industryType.plac": "请选择关联行业类型", "classGrading.tab.task.template.plac": "请选择分类分级模板", "classGrading.tab.task.executeType.value1": "引擎识别", "classGrading.tab.task.executeType.value2": "大模型识别", "classGrading.tab.task.executeStrategy.value1": "全量", "classGrading.tab.task.executeStrategy.value2": "增量", "classGrading.tab.task.scheduleCycle.plac": "请选择调度周期", "classGrading.tab.task.executeTime": "执行时间", "classGrading.tab.task.executeTime.plac": "请选择执行时间", "classGrading.export.fileName.label": "文件名称", "classGrading.export.fileName.plac": "请输入文件名称", "classGrading.export.fileName.hint": "请输入以.csv结尾的文件名", "classGrading.tab.tag.addIndustryType": "新增行业类型", "classGrading.tab.tag.searchPlac": "请输入标签名称搜索", "classGrading.tab.tag.column.tagName": "标签名称", "classGrading.tab.tag.column.industryType": "行业类型", "classGrading.tab.tag.column.totalMatchWeight": "总匹配权重", "classGrading.tab.tag.column.fieldName": "字段列名称", "classGrading.tab.tag.column.fieldRemark": "字段列备注", "classGrading.tab.tag.column.tableName": "表名称", "classGrading.tab.tag.column.tableRemark": "表备注", "classGrading.tab.tag.column.dataLength": "数据长度", "classGrading.tab.tag.column.keyword": "包含关键字", "classGrading.tab.tag.column.notKeyword": "不包含关键字", "classGrading.tab.tag.column.regular": "正则表达式", "classGrading.tab.tag.column.sensitive": "个人敏感信息", "classGrading.tab.tag.column.industry": "行业敏感信息", "classGrading.tab.tag.column.importantData": "重要数据", "classGrading.tab.tag.column.coreData": "核心数据", "classGrading.tab.tag.column.modifyTime": "最近修改时间", "classGrading.tab.tag.column.tagStatus": "标签状态", "classGrading.tab.tag.addTag": "新增标签", "classGrading.tab.tag.name.label": "名称", "classGrading.tab.tag.name.plac": "请输入名称", "classGrading.tab.tag.desc.label": "描述", "classGrading.tab.tag.desc.plac": "请输入描述信息", "classGrading.tab.tag.createTag.label": "创建数据源标签", "classGrading.tab.tag.createTag.plac": "请选择数据源标签", "classGrading.tab.tag.dataSource.label": "数据源", "classGrading.tab.tag.dataSource.plac": "请选择数据源", "classGrading.tab.tag.industry.label": "行业信息", "classGrading.tab.tag.industry.plac": "请选择行业信息", "classGrading.tab.tag.file.label": "选择文件", "classGrading.tab.tag.file.btnText": "文件上传", "classGrading.tab.tag.file.plac": "请上传文件", "classGrading.tab.tag.identSample.label": "标识例样", "classGrading.tab.tag.indentTemplate.label": "标识模板", "classGrading.tab.tag.tagName": "标签", "classGrading.tab.tag.gradeConfig": "等级配置", "classGrading.tab.tag.level": "标签等级", "classGrading.tab.tag.category": "标签分类", "classGrading.tab.template.delete.tip": "当前节点存在子节点，不允许删除", "classGrading.tab.template.delete.tip2": "当前分类存在关联标签，不允许删除", "classGrading.tab.template.editClass": "编辑分类", "classGrading.tab.template.addClass": "新增分类", "classGrading.tab.template.className.label": "分类名称", "classGrading.tab.template.className.plac": "请输入分类名称", "classGrading.tab.template.editTemplate": "编辑模板", "classGrading.tab.template.addTemplate": "新增模板", "classGrading.tab.template.templateName.label": "模板名称", "classGrading.tab.template.templateName.plac": "请输入模板名称", "classGrading.tab.template.tagType.label": "标签类型", "classGrading.tab.template.tagType.plac": "请选择标签类型", "classGrading.tab.template.relatedTemplate.label": "关联分级模板", "classGrading.tab.template.relatedTemplate.plac": "请选择关联分级模板", "classGrading.tab.template.level.label": "级别", "classGrading.tab.template.level.plac": "请选择级别", "classGrading.tab.template.connMethod.label": "关联方式", "classGrading.tab.template.connMethod.plac": "请选择关联方式", "classGrading.tab.template.asscoTag.label": "关联标签导入例样", "classGrading.tab.template.asscoDatasouce.label": "关联标签导入例样"}