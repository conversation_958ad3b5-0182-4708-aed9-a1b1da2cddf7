import React, { useEffect, useState } from "react";
import { RedoOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import i18n from "i18next";
import { useTranslation } from "react-i18next";
import { Table, Button, Tooltip, Typography, message } from "antd";
import { useDispatch, useSelector, useRequest } from 'src/hook';
import { UIModal } from "src/components";
import {
  ClassResTableGradeDetailAPI,
  ClassResTableSetManualGradeAPI
} from 'src/api';
import { hideModal } from "src/store/extraSlice/modalVisibleSlice";
import ManualGradingModal from './ManualGradingModal';
import { getTablePaginatinLimitFiled } from '../../utils';

export default () => {

  const defaultTabParams = {
    pageSize: 10,
    pageNum: 1
  }
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const { ClassificationDetailModal } = useSelector((state) => state.modal)
  const { visible = false, extraProps } = ClassificationDetailModal || {};


  const [tableParams, setTableParams] = useState(defaultTabParams);
  const [manualGradingModalVisible, setManualGradingModalVisible] = useState(false);

  //列表
  const { data: gradeData, loading: listLoading, run: runGetGradeList, refresh: refreshGradeList } = useRequest((params: any) => {

    let newParams = {
      ...params,
      limit: getTablePaginatinLimitFiled(params?.pageNum, params?.pageSize)
    }

    delete newParams?.pageSize;
    delete newParams?.pageNum;
    return ClassResTableGradeDetailAPI(newParams);
  }, {
    manual: true,
    debounceInterval: 300,
    formatResult: (res: any) => {
      return {
        total: res?.count?.data?.[0]?.[0] || 0,
        list: res?.datas ?? []
      }
    }
  });

  //设定人工定级
  const {run: runSetManualGrade } = useRequest(ClassResTableSetManualGradeAPI, {
    manual: true,
    onSuccess: () => {
      message.success(t('common.message.testSuccessfully'));
    }
  });

  useEffect(() => { 
    visible && runGetGradeList({...tableParams})
  }, [tableParams, visible]);
  const onSettingManualGrading = (classify_id: number) => {
    runSetManualGrade({classify_id});
  }

  if (!visible) return null;

  const newColumn = () => {
    return columns({
      onSettingManualGrading
    }).map(column => (
      {
        ...column,
        render: (val: any) => {
          return (
            <Tooltip title={val}>
              <Typography.Text ellipsis>{val}</Typography.Text>
            </Tooltip>
          )
        }
      }
    ))
  }
  return (
    <UIModal
      title={t('classGrading.tab.result.column.view')}
      visible={true}
      width={900}
      onCancel={() => dispatch(hideModal('ClassificationDetailModal'))}
      destroyOnClose={true}
      footer={null}
    >
      <div className='flexJustifyEnd mb10'>
        <Button icon={<RedoOutlined />} onClick={() => { refreshGradeList() }}></Button>
      </div>
      <Table
        size="small"
        bordered={true}
        loading={listLoading}
        columns={newColumn()}
        dataSource={gradeData?.list || []}
        scroll={{ x: 'max-content', y: `calc(100vh - 400px)` }}
        pagination={{
          showQuickJumper: true,
          showSizeChanger: true,
          current: tableParams.pageNum || 1,
          pageSize: tableParams.pageSize || 10,
          total: gradeData?.total || 0,
          showTotal: (total) => t('common.table.pagination.total', { total }),
          onChange: (pageNum, pageSize = 10) => {
            setTableParams({ ...tableParams, pageNum, pageSize })
          }
        }}
      />
      {/* 设定为人工定级弹框 */}
      {
        manualGradingModalVisible &&
        <ManualGradingModal
         onCancel={() => setManualGradingModalVisible(false)}
        />
      }
    </UIModal>
  )
}

const columns = ({
  onSettingManualGrading
}: {
  onSettingManualGrading: (id: number) => void
}) : ColumnsType<any[]> => [
    {
      title: i18n.t('classGrading.tab.result.column.tableName'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: i18n.t('classGrading.tab.result.column.class'),
      dataIndex: 'age',
      key: 'age',
    },
    {
      title: i18n.t('classGrading.tab.result.column.radio'),
      dataIndex: 'address',
      key: 'address',
    },
    {
      title: i18n.t('classGrading.tab.result.column.count'),
      dataIndex: 'address',
      key: 'address',
    },
    {
      title: i18n.t('classGrading.tab.result.column.level'),
      dataIndex: 'address',
      key: 'address',
    },
    {
      title: i18n.t('classGrading.tab.result.column.isOverThreshold'),
      dataIndex: 'address',
      key: 'address',
      width: 120,
    },
    {
      title: i18n.t('classGrading.tab.result.column.isManual'),
      dataIndex: 'address',
      key: 'address',
      width: 120,
    },
    {
      title: i18n.t('common.text.action'),
      dataIndex: 'address',
      key: 'address',
      fixed: 'right',
      width: 100,
      render: (_: any, record: any) => (
        <Button
          type="link"
          className="padding0"
          onClick={() => onSettingManualGrading(record?.classify_id)}
        >
          {i18n.t('classGrading.tab.result.column.manual')}
        </Button>
      )
    },
  ]