import React, { useEffect, useState } from "react";
import { RedoOutlined } from '@ant-design/icons';
import i18n from "i18next";
import { useTranslation } from "react-i18next";
import { Table, Button, Tooltip, Typography } from "antd";
import { UIModal } from "src/components";
import {
  ClassResTableColumnDetailAPI
} from 'src/api';
import { useDispatch, useSelector, useRequest } from 'src/hook';
import { hideModal } from "src/store/extraSlice/modalVisibleSlice";
import { getTablePaginatinLimitFiled } from '../../utils';

export default () => {

  const defaultTabParams = {
    pageSize: 10,
    pageNum: 1
  }
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const { ClassResultColumnDetailModal } = useSelector((state) => state.modal)
  const { visible = false, extraProps } = ClassResultColumnDetailModal || {};


  const [tableParams, setTableParams] = useState(defaultTabParams);
  //列表
  const { data: columnData, loading: listLoading, run: runGetColumnListList, refresh: refreshTableColumnList } = useRequest((params: any) => {

    let newParams = {
      ...params,
      limit: getTablePaginatinLimitFiled(params?.pageNum, params?.pageSize)
    }

    delete newParams?.pageSize;
    delete newParams?.pageNum;
    return ClassResTableColumnDetailAPI(newParams);
  }, {
    manual: true,
    debounceInterval: 300,
    formatResult: (res: any) => {
      return {
        total: res?.count?.data?.[0]?.[0] || 0,
        list: res?.datas ?? []
      }
    }
  });

  useEffect(() => {
    visible && runGetColumnListList({ ...tableParams });
  }, [tableParams, visible])

  if (!visible) return null;

  return (
    <UIModal
      title={t('classGrading.tab.result.columnInfo')}
      visible={true}
      width={900}
      onCancel={() => dispatch(hideModal('ClassResultColumnDetailModal'))}
      destroyOnClose={true}
      footer={null}
    >
      <div className='flexJustifyEnd mb10'>
        <Button icon={<RedoOutlined />} onClick={() => { refreshTableColumnList() }}></Button>
      </div>
      <Table
        size="small"
        bordered={true}
        loading={listLoading}
        columns={columns}
        dataSource={columnData?.list || []}
        pagination={{
          showQuickJumper: true,
          showSizeChanger: true,
          current: tableParams.pageNum || 1,
          pageSize: tableParams.pageSize || 10,
          total: columnData?.total || 0,
          showTotal: (total) => t('common.table.pagination.total', { total }),
          onChange: (pageNum, pageSize = 10) => {
            setTableParams({ ...tableParams, pageNum, pageSize })
          }
        }}
      />
    </UIModal>
  )
}

const columns = [
  {
    title: i18n.t('classGrading.tab.result.column.tableName'),
    dataIndex: 'table_name',
    key: 'table_name',
    render: (val: string) => (
      <Tooltip title={val}>
        <Typography.Text ellipsis>{val}</Typography.Text>
      </Tooltip>
    )
  },
  {
    title: i18n.t('classGrading.tab.result.column.columnName'),
    dataIndex: 'column_name',
    key: 'column_name',
    render: (val: string) => (
      <Tooltip title={val}>
        <Typography.Text ellipsis>{val}</Typography.Text>
      </Tooltip>
    )
  },
  {
    title: i18n.t('classGrading.tab.result.column.columnDesc'),
    dataIndex: 'column_comment',
    key: 'column_comment',
    render: (val: string) => (
      <Tooltip title={val}>
        <Typography.Text ellipsis>{val}</Typography.Text>
      </Tooltip>
    )
  },
  {
    title: i18n.t('classGrading.tab.result.column.className'),
    dataIndex: 'classify_name',
    key: 'classify_name',
    render: (val: string) => (
      <Tooltip title={val}>
        <Typography.Text ellipsis>{val}</Typography.Text>
      </Tooltip>
    )
  },
  {
    title: i18n.t('classGrading.tab.result.column.level'),
    dataIndex: 'level_name',
    key: 'level_name',
    render: (val: string) => (
      <Tooltip title={val}>
        <Typography.Text ellipsis>{val}</Typography.Text>
      </Tooltip>
    )
  },
]