import React from "react";
import { ExclamationCircleTwoTone, CheckCircleTwoTone } from '@ant-design/icons'
import classNames from "classnames";
import { useTranslation } from 'react-i18next';
import type { ColumnsType } from 'antd/es/table';
import { Button, Space, Popconfirm } from 'antd';
import { IClassTaskExecuteTaskParams } from 'src/api';
import { ExecutionStatus, TaskSheduleCycle } from '../types';
import {
  EXECUTION_STATUS_DISPLAY_ENUM,
  TASK_MODE_MAPPING,
  EXECUTE_TYPE_MAPPING,
  CLASSIFICATION_SCHEDULE_CYCLE
} from '../constants';
import styles from './index.module.scss';

export default ({
  onEdit,
  onExecuteTask,
  onDeleteTask,
  onHandleLinkToResult
}: {
  onEdit: (record: any) => void
  onExecuteTask: (record: IClassTaskExecuteTaskParams) => void
  onDeleteTask: (id: number) => void
  onHandleLinkToResult: (record: any) => void
}): ColumnsType<any> => {

  const { t } = useTranslation();

  return [
    {
      title: t('classGrading.tab.task.column.taskName'),
      dataIndex: 'task_name',
      key: 'task_name',
      width: 150,
    },
    {
      title: t('classGrading.tab.task.column.dataSource'),
      dataIndex: 'src_id',
      key: 'src_id',
      width: 150,

    },
    {
      title: t('classGrading.tab.task.column.executeType'),
      dataIndex: 'task_type',
      key: 'task_type',
      width: 150,
      render: (val: number) => EXECUTE_TYPE_MAPPING[val]
    },
    {
      title: t('classGrading.tab.task.column.executeStrategy'),
      dataIndex: 'task_mode',
      width: 150,
      key: 'task_mode',
      render: (val: number) => TASK_MODE_MAPPING[val]
    },
    {
      title: t('classGrading.tab.task.column.scheduleCycle'),
      dataIndex: 'task_schedule',
      width: 150,
      key: 'task_schedule',
      render: (val:TaskSheduleCycle) => CLASSIFICATION_SCHEDULE_CYCLE[val]
    },
    {
      title: t('common.text.status'),
      dataIndex: 'task_state',
      key: 'task_state',
      width: 150,
      render: (val: ExecutionStatus) => {
     
        return (
          <div className={classNames(styles.status, styles[`status-${val}`])}>
            <span>
              {
                val === 'failed' && <ExclamationCircleTwoTone twoToneColor={'#E55174'} className="mr4" />
              }
              {
                val === 'success' && <CheckCircleTwoTone twoToneColor={'#41AA9F'} className="mr4" />
              }
              {EXECUTION_STATUS_DISPLAY_ENUM[val]}
            </span>
          </div>
        )
      }
    },
    {
      title: t('classGrading.tab.task.column.industryType'),
      dataIndex: 'type_sb',
      width: 150,
      key: 'type_sb'
    },
    {
      title: t('classGrading.tab.task.column.template'),
      dataIndex: 'classifys_id',
      width: 150,
      key: 'classifys_id'
    },
    {
      title: t('common.text.action'),
      dataIndex: 'action',
      key: 'action',
      fixed: 'right',
      width: 200,
      render: (_: any, record: any) => {
        return (
          <Space>
            <Button type='link' className="padding0" onClick={() => onEdit(record)}>{t('common.btn.edit')}</Button>
            <Button
              type='link'
              className="padding0"
              onClick={() => onExecuteTask({ classifys_id: record?.classifys_id, id: record?.id, recon_id: record?.recon_id })}
            >
              {t('classGrading.tab.task.column.executeTask')}
            </Button>
            <Button type='link' className="padding0" onClick={() => onHandleLinkToResult(record)}>{t('classGrading.tab.task.column.recognitionResult')}</Button>
            <Popconfirm
              title={t('common.modal.delete.content')}
              onConfirm={() => { record?.id && onDeleteTask(record.id) }}
            >
              <Button type='link' danger className="padding0">{t('common.btn.delete')}</Button>
            </Popconfirm>
          </Space>
        )
      }
    },
  ]
}