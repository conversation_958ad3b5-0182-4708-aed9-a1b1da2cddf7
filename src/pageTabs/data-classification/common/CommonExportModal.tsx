import React from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, Form, Input, Select, message } from 'antd';
import type { FormInstance } from 'antd/es/form';
import { useRequest } from 'src/hook';
import { UIModal } from 'src/components';
import { FormLayout, INDDUSTRY_TAG_TYPE, CreateIndustryTagType } from '../constants';

interface TemplateFormValues {
  templateName: string;
  tagType?: string;
  relatedTemplate?: string;
}

export default ({
  submitBtnLoading = false,
  onExport,
  onCancel
}: {
  submitBtnLoading?: boolean;
  onExport: (params: any) => void;
  onCancel: () => void;
}) => {

  const { t } = useTranslation();
  const [industryForm] = Form.useForm<TemplateFormValues>();

  const onSubmit = () => {
    industryForm.validateFields().then((values) => {

      onExport(values)
    })
  };

  return (
    <UIModal
      title={t('common.btn.export')}
      visible={true}
      onOk={onSubmit}
      onCancel={onCancel}
      confirmLoading={submitBtnLoading}
      width={600}
    >
      <Form form={industryForm} {...FormLayout}>
        <Form.Item
          label={t('classGrading.export.fileName.label')}
          name="templateName"
          rules={[

            { required: true, pattern: /\.csv$/, message: t('classGrading.export.fileName.hint') }
          ]}
        >
          <Input placeholder={t('classGrading.export.fileName.plac')} />
        </Form.Item>
      </Form>
    </UIModal>
  );
}
