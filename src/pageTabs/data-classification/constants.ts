import i18n from 'i18next';
import { TemplateTab, NewTemplateOperation, ClassificationTab, ClassificationRes } from './types';

export const DATA_CLASSIFICATION_TAB: Record<ClassificationTab, string> = {
  templateManagement: i18n.t('classGrading.tab.template.title'),
  tagManagement: i18n.t('classGrading.tab.tag.title'),
  classificationTask: i18n.t('classGrading.tab.task.title'),
  classificationResult: i18n.t('classGrading.tab.result.title')
} as const;


// 定义 tabs 对象
export const TEMPLATE_TABS: Record<TemplateTab, string> = {
  builtInTemplate: i18n.t('classGrading.tab.template.tab.builtIn'),
  newTemplate: i18n.t('classGrading.tab.template.tab.new'),
};

//新增模板 操作
export const NEW_TEMPLATE_OPERATIONS: Record<NewTemplateOperation, string> = {
  addClass: i18n.t('classGrading.tab.template.action.addClass'),
  delete: i18n.t('common.btn.delete'),
  edit: i18n.t('common.btn.edit'),
  addTemplate: i18n.t('classGrading.tab.template.tab.new'),
  gradeConfig: i18n.t('classGrading.tab.template.action.gradeConfig'),
  importTemplate: i18n.t('classGrading.tab.template.action.importTemplate')
}

export const FormLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
}

export type CreateIndustryTagType = 1 | 2;
// 行业枚举类型定义 是否创建数据标签
export const INDDUSTRY_TAG_TYPE: Record<CreateIndustryTagType, string> =  {
  2: i18n.t('common.btn.yes'),
  1: i18n.t('common.btn.no')
};
// 分类分级 --------------------------------------------- start ---------------------------

//分级分类 调度周期枚举类型定义 天数
export enum TaskSheduleCycleEnum {
  MANUAL = 10000,
  DAY = 1, 
  TWO_DAY = 2,
  THREE_DAY = 3,
  WEEK = 7,
  TWO_WEEK = 14,
  THREE_WEEK = 21,
}
export const CLASSIFICATION_SCHEDULE_CYCLE : Record<TaskSheduleCycleEnum, string> = {
  [TaskSheduleCycleEnum.MANUAL]:i18n.t('classGrading.tab.task.timeCycle.MANUAL'),
  [TaskSheduleCycleEnum.DAY]: i18n.t('classGrading.tab.task.timeCycle.DAY'),
  [TaskSheduleCycleEnum.TWO_DAY]: i18n.t('classGrading.tab.task.timeCycle.TWO_DAY'),
  [TaskSheduleCycleEnum.THREE_DAY]: i18n.t('classGrading.tab.task.timeCycle.THREE_DAY'),
  [TaskSheduleCycleEnum.WEEK]: i18n.t('classGrading.tab.task.timeCycle.WEEK'),
  [TaskSheduleCycleEnum.TWO_WEEK]: i18n.t('classGrading.tab.task.timeCycle.TWO_WEEK'),
  [TaskSheduleCycleEnum.THREE_WEEK]: i18n.t('classGrading.tab.task.timeCycle.THREE_WEEK'),
}

//分类分级任务 标签状态
export  enum ExecutionStatusEnum {
  NOT_STARTED = 0,
  PENDING = 1,
  IN_PROGRESS = 2,
  DELETE = 3,
  COMPLETED = 9,
  SUCCESS = 'success',
  FAILED = 'failed',
}

export const EXECUTION_STATUS_DISPLAY_ENUM: Record<ExecutionStatusEnum, string> = {
  [ExecutionStatusEnum.NOT_STARTED]: i18n.t('classGrading.tab.task.status.notStarted'),
  [ExecutionStatusEnum.PENDING]: i18n.t('classGrading.tab.task.status.pending'),
  [ExecutionStatusEnum.IN_PROGRESS]: '进行中',
  [ExecutionStatusEnum.DELETE]: '关联任务已删除',
  [ExecutionStatusEnum.COMPLETED]: '已完成',
  [ExecutionStatusEnum.SUCCESS]: i18n.t('classGrading.tab.task.status.success'),
  [ExecutionStatusEnum.FAILED]: i18n.t('classGrading.tab.task.status.failed')
};
//执行策略
export const TASK_MODE_MAPPING: Record<number, string>  = {
  1: i18n.t('classGrading.tab.task.executeStrategy.value2'),
  2: i18n.t('classGrading.tab.task.executeStrategy.value1'),
}
//执行类型
export const EXECUTE_TYPE_MAPPING: Record<number, string>  = {
  0: i18n.t('classGrading.tab.task.executeType.value1'),
  1: i18n.t('classGrading.tab.task.executeType.value2'),
}

//识别方式
export const TASK_TYPE_MAPPING: Record<number, string>  = {
  1: '识别本地数据库',
  2: '识别业务数据库',
}
// 分类分级 ----------------------------------------------- end --------------------------

//分级结果 

export const CLASSIFICATION_RES: Record<ClassificationRes, string> = {
  ALL: i18n.t('classGrading.tab.result.res.all'),
  GRADED: i18n.t('classGrading.tab.result.res.grade'),
  UNGRADED: i18n.t('classGrading.tab.result.res.unGrade'),
} as const;


//查询行业列表默认值
export const FetchIndustryListParams = {
  action: 'get_industry_types',
  key: 'dd:label_tree'
}
