import React, { useEffect, useState } from "react";
import { MoreOutlined, PlusSquareTwoTone, TrademarkOutlined } from '@ant-design/icons';
import classNames from 'classnames';
import { useTranslation } from "react-i18next";
import { Tree, Spin, Dropdown, Menu, Tooltip, Modal, message } from 'antd';
import { useDispatch, useRequest } from "src/hook";
import {
  ClassTagEditIndustry,
  ClassTagIndustryListAPI,
  ClassTagDeleteIndustryAPI,
  ClassTagAddIndustry
} from 'src/api';
import { useClassificationTagContext } from './ClassificationTagContext';
import { AddOrEditIndustryModal } from "./modals";
import { IClassificationTreeOperation } from '../types';
import { FetchIndustryListParams } from '../constants';
import styles from './index.module.scss';

const maxTreeCount = 1000000;

export default () => {

  const { t } = useTranslation();
  const dispatch = useDispatch();

  const { selectedTreeNodeInfo, setSelectedTreeNodeInfo } = useClassificationTagContext();

  const [addOrEditTreeNodeModalVisible, setAddOrEditTreeNodeModalVisible] = useState<boolean>(false);
  const [addOrEditTreeNodeInfo, setAddOrEditTreeNodeInfo] = useState<any>(null);

  const { data: classificationTreeData, loading: treeLoading, run: runClassIndustryList } = useRequest(ClassTagIndustryListAPI,
    {
      manual: true,
      onSuccess: (res) => {

      
        const childNode = res?.[0];
        setSelectedTreeNodeInfo(childNode);
        
        if (addOrEditTreeNodeModalVisible) {
          setAddOrEditTreeNodeInfo(null);
          setAddOrEditTreeNodeModalVisible(false);
        }
      },
      formatResult(res: any) {
        const datas = res?.datas || [];
        const formattedTreeData = datas.map((item: any) => ({
          ...item,
          title: item?.name,
          key: item?.id,
        }))

        return formattedTreeData
      },
  });

  const {run: runClassTagDeleteIndustryAPI} = useRequest(ClassTagDeleteIndustryAPI, {manual: true})

  useEffect(() => {
    runClassIndustryList({ limit: maxTreeCount })
  }, [])

  const onAddOrEditIndustry = (params: any) => {
    let action = ClassTagAddIndustry;
    if (addOrEditTreeNodeInfo) {
      action = ClassTagEditIndustry;
    }
    action(params).then(() => {
      if (addOrEditTreeNodeInfo) {
        message.success(t('common.message.edit.success'));
      } else {
        message.success(t('common.message.addSuccess'));
      }
      runClassIndustryList({ limit: maxTreeCount })
    })
  }

  const onHandleMoreOperations = (key: IClassificationTreeOperation, node?: any) => {
    switch (key) {
      case 'edit':
        setAddOrEditTreeNodeInfo(node);
        setAddOrEditTreeNodeModalVisible(true);

        break;
      case 'add':
        setAddOrEditTreeNodeModalVisible(true);
        break;
      case 'delete':
        Modal.confirm({
          centered: true,
          title: t('common.text.delete.tip'),
          onOk: async () => {
            // 删除逻辑
            runClassTagDeleteIndustryAPI({
             id: node?.id
            }).then(() => {
              message.success(t('common.message.delete_success'));
              runClassIndustryList({ limit: maxTreeCount })
            })
          },
        });
        break;
      default:
        break;
    }
  }
  const treeMenu = (node: any) => {

    return (
      <Menu className={styles.optionTxt} onClick={({ key }) => { onHandleMoreOperations(key as IClassificationTreeOperation, node) }}>
        <Menu.Item key='edit'>
          {t('common.btn.edit')}
        </Menu.Item>
        <Menu.Item key='delete'>
          {t('common.btn.delete')}
        </Menu.Item>
      </Menu>
    )
  }
  const titleRender = (node: any) => {

    return (
      <div className={styles.treeTitleItem}>
        <div className={styles.titleTxtWrap}>
          <span className={styles.titleTxt}>{node?.title}</span>
        </div>
        <Dropdown overlay={() => treeMenu(node)}>
          <MoreOutlined className={classNames(styles.ml10, styles.options)} />
        </Dropdown>
      </div>
    )
  }
console.log(classificationTreeData , '==')
  return (
    <>
      <div className="flexAlignCenterBetween mb10">
        <h3>行业类型</h3>
        <Tooltip title={t('classGrading.tab.tag.addIndustryType')}>
          <PlusSquareTwoTone onClick={() => { onHandleMoreOperations('add') }} />
        </Tooltip>
      </div>
      <Spin spinning={treeLoading} wrapperClassName={styles.spinContainer}>
        <Tree
          blockNode
          treeData={classificationTreeData}
          className={styles.tree}
          selectedKeys={selectedTreeNodeInfo?.key ? [selectedTreeNodeInfo.key] : []}
          titleRender={titleRender}
          onSelect={(keys, e) => {
            setSelectedTreeNodeInfo({ ...e.node })
          }}
        />
        {!treeLoading && !classificationTreeData?.length && (
        <div className={styles.treePlaceholder}>{t('db.auth.noElement')}</div>
      )}
      </Spin>

      {/* 新增或编辑 */}
      {
        addOrEditTreeNodeModalVisible &&
        <AddOrEditIndustryModal
          editTtemInfo={addOrEditTreeNodeInfo}
          onSubmit={(params: any) => { onAddOrEditIndustry(params) }}
          onCancel={() => { setAddOrEditTreeNodeModalVisible(false); setAddOrEditTreeNodeInfo(null); }}
        />
      }

    </>
  )
}