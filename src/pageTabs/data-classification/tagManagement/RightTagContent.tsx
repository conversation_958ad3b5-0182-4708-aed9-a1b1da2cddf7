import React, { useEffect, useState } from 'react';
import { RedoOutlined, DownOutlined } from '@ant-design/icons'
import * as _ from 'lodash';
import { useRequest } from 'src/hook';
import { Table, Space, Button, Dropdown, Menu, Switch, Popconfirm, Input, Tooltip, message, Typography } from 'antd'
import { useTranslation } from 'react-i18next';
import type { TableRowSelection } from "antd/es/table/interface"
import type { ColumnsType } from 'antd/es/table';
import {
  ClassClassTagAPI,
  ClassImportTagAPI
} from 'src/api';
import {
  ImportTemplateModa,
  TagModal,
  ExportTagModal,
} from './modals';
import { useClassificationTagContext } from './ClassificationTagContext';
import { getTablePaginatinLimitFiled } from '../utils';
import styles from './index.module.scss';

interface TagItem {
  key: string;
  name: string;
  level: string;
  category: string;
}

type ITableParams = {
  label_name?: string;
  pageSize: number;
  pageNum: number;
  type?: number;
}
const TagManagementTable: React.FC = () => {

  const defaultPagination = {
    pageSize: 10,
    pageNum: 1,
  }

  const { t } = useTranslation();
  const { selectedTreeNodeInfo } = useClassificationTagContext();

  const [tableParams, setTableParams] = useState<ITableParams>(defaultPagination);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  //导入模板
  const [importTagModalVisible, setImportTagModalVisible] = useState<boolean>(false);
  //导出模板
  const [exportTagModalVisible, setExportTagModalVisible] = useState<boolean>(false);
  //标签
  const [tagModalVisible, setTagModalVisible] = useState<boolean>(false);
  const [editTagInfo, setEditTagInfo] = useState<any>(null);

  //列表
  const { data: tagData, loading: listLoading, run: runGetTagList, refresh: refreshList } = useRequest((params: any) => {

    let newParams = {
      action: 'query_tag_list',
      label_id: '',
      label_name: '',
      ...params,
      limit: getTablePaginatinLimitFiled(params?.pageNum, params?.pageSize)
    }

    delete newParams?.pageSize;
    delete newParams?.pageNum;
    return ClassClassTagAPI(newParams);
  }, {
    manual: true,
    debounceInterval: 300,
    formatResult: (res: any) => {
      return {
        total: res?.count?.data?.[0]?.[0] || 0,
        list: res?.datas ?? []
      }
    }
  });
  //导出模板
  const { loading: exportBtnLoading, run: runClassTagExport } = useRequest('', {
    onSuccess: () => {
      message.success(t('common.message.export_success'));
    }
  });
  //删除
  const onDeleteTag = (id: number) => {
    ClassClassTagAPI({
      action: 'delete_tag',
      id,
    }).then(() => {
      message.success(t('common.message.delete_success'));
      setTableParams({ ...tableParams, pageNum: 1 });
    })
  }

  //修改标签状态
  const { run: runChangeTagStatus } = useRequest('', {
    onSuccess: () => {
      message.success(t('common.message.editSuccess'));
      refreshList();
    }
  });

  useEffect(() => {
    setTableParams({ ...tableParams, type: selectedTreeNodeInfo?.id });
  }, [selectedTreeNodeInfo?.id])

  useEffect(() => {
    if (!tableParams?.type) return
    runGetTagList({ ...tableParams })
  }, [tableParams])

  //批量导出
  const onBatchExportTemplate = (values: any) => {
    runClassTagExport()
  }
  const onEdit = (record: any) => {
    setEditTagInfo(record);
    setTagModalVisible(true);
  }

  const onAddOrEditTag = async (params: any) => {
    ClassClassTagAPI(params).then(() => {
      if (editTagInfo) {
        message.success(t('common.message.edit.success'));
      } else {
        message.success(t('common.message.addSuccess'));
      }
      setTagModalVisible(false);
      setEditTagInfo(null);
      setTableParams({ ...tableParams, pageNum: 1 });
    })
  }


  const onHandleImportTag = (values: any) => {
    ClassImportTagAPI(values)
  }

  const onChangeTagStatus = (params: any) => {
    let newParams = {
      ids: params?.ids,
      type: selectedTreeNodeInfo?.type,
      action: params?.status ? 'batch_enable_tags' : 'batch_disable_tags'
    }
    ClassClassTagAPI(newParams).then(() => {
      message.success(t('common.message.editSuccess'));
      setTableParams({ ...tableParams });
      setSelectedRowKeys([]);
    })

  }

  const onHandleMoreOperations = (key: any) => {
    switch (key) {
      case 'EXPORT':
        setExportTagModalVisible(true);
        break;
      case 'ENABLE':
        onChangeTagStatus({ ids: selectedRowKeys.join('|'), status: true });
        break;
      case 'FORBIDDEN':
        onChangeTagStatus({ ids: selectedRowKeys.join('|'), status: false });
        break;
      default:
        break;
    }
  }

  const moreBatchOperations = () => {
    return (
      <Menu className={styles.optionTxt} onClick={({ key }) => onHandleMoreOperations(key)}>
        <Menu.Item key="EXPORT">{t('common.btn.batchExport')}</Menu.Item>
        <Menu.Item key="ENABLE">{t('common.btn.batchEnable')}</Menu.Item>
        <Menu.Item key="FORBIDDEN">{t('common.btn.batchForbid')}</Menu.Item>
      </Menu>
    )
  }

  //翻页多选
  const rowSelection: TableRowSelection<any> = {
    selectedRowKeys: selectedRowKeys,
    onSelectAll(selected, newSelectedRows: any[]) {

      const curRowKeys = newSelectedRows.map(row => row?.label_id);

      let cloneSelectedRowKeys = _.cloneDeep(selectedRowKeys);
      if (selected) {
        cloneSelectedRowKeys = cloneSelectedRowKeys.concat(curRowKeys)
      } else {
        const curKeys = tagData?.list?.map((row: any) => row?.label_id) || [];
        cloneSelectedRowKeys = cloneSelectedRowKeys.filter(k => !curKeys.includes(k))

      }
      setSelectedRowKeys([...new Set(cloneSelectedRowKeys)]);
    },
    onSelect(item, selected) {

      let cloneSelectedRowKeys = _.cloneDeep(selectedRowKeys);
      if (selected) {
        cloneSelectedRowKeys = cloneSelectedRowKeys.concat([item.label_id])
      } else {
        cloneSelectedRowKeys = cloneSelectedRowKeys.filter(k => k !== item.label_id)
      }
      setSelectedRowKeys(cloneSelectedRowKeys);
    },
  };

  return (
    <div className={styles.rightTemplateContent}>
      <div className={styles.title}>
        <Tooltip title={selectedTreeNodeInfo?.details ?? '-'}>
          <Typography.Text ellipsis>{selectedTreeNodeInfo?.details ?? '-'}</Typography.Text>
        </Tooltip>
      </div>
      <div className={styles.header}>
        <Input.Search
          allowClear
          placeholder={t('classGrading.tab.tag.searchPlac')}
          onChange={(val) => setTableParams({ ...tableParams, pageNum: 1, label_name: val.target.value })}
          className={styles.searchInput}
        />
        <Space>
          <Button type='primary' onClick={() => setTagModalVisible(true)}>{t('classGrading.tab.tag.addTag')}</Button>
          <Button type='primary' onClick={() => setImportTagModalVisible(true)}>{t('common.btn.import')}</Button>
          <Dropdown overlay={moreBatchOperations} disabled={!selectedRowKeys?.length}>
            <Button disabled={!selectedRowKeys?.length}>{t('common.btn.batchText')} <DownOutlined /></Button>
          </Dropdown>
          <Button icon={<RedoOutlined />} onClick={() => refreshList()} />
        </Space>
      </div>
      <Table
        rowKey="label_id"
        loading={listLoading}
        columns={columns({
          onChangeTagStatus,
          onEdit,
          onDelete: (id: number) => onDeleteTag(id)
        })}
        dataSource={tagData?.list || []}
        className={styles.table}
        scroll={{
          y: `calc(100vh - 500px)`,
          x: 'max-content' // 添加横向滚动
        }}
        rowSelection={rowSelection}
        pagination={{
          showQuickJumper: true,
          showSizeChanger: true,
          pageSize: tableParams?.pageSize || 10,
          current: tableParams?.pageNum || 1,
          total: tagData?.total ?? 0,
          showTotal: (total) => t('common.table.pagination.total', { total }),
          onChange: (pageNumber, pageSize = 10) => {
            setTableParams({ ...tableParams, pageNum: pageNumber, pageSize });
          },
        }}
      />
      {/* 导出模板 */}
      {
        exportTagModalVisible &&
        <ExportTagModal
          submitBtnLoading={exportBtnLoading}
          onExport={(values: any) => onBatchExportTemplate(values)}
          onCancel={() => { setExportTagModalVisible(false); }}
        />
      }
      {/* 导入模板 */}
      {
        importTagModalVisible &&
        <ImportTemplateModa
          onCancel={() => { setImportTagModalVisible(false); }}
          onSubmit={onHandleImportTag}
        />
      }
      {/* 标签 */}
      {
        tagModalVisible &&
        <TagModal
          editTagInfo={editTagInfo}
          onCancel={() => { setTagModalVisible(false); setEditTagInfo(null); }}
          onSubmit={onAddOrEditTag}
        />
      }
    </div>
  );
};

export default TagManagementTable;

const columns = ({
  onChangeTagStatus,
  onEdit,
  onDelete
}: {
  onChangeTagStatus: (params: any) => void;
  onEdit: (record: any) => void;
  onDelete: (id: number) => void;
}): ColumnsType<TagItem> => {

  const { t } = useTranslation();

  return ([
    {
      title: t('classGrading.tab.tag.column.tagName'),
      dataIndex: 'label_name',
      key: 'label_name',
      width: 200,
      render: (text) => <div className={'linkStyle'}>{text}</div>
    },
    {
      title: t('classGrading.tab.tag.column.industryType'),
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: t('classGrading.tab.tag.column.totalMatchWeight'),
      dataIndex: 'threshold',
      key: 'threshold',
      width: 150,
    },
    {
      title: t('classGrading.tab.tag.column.fieldName'),
      dataIndex: 'colname',
      key: 'colname',
      width: 180,
    },
    {
      title: t('classGrading.tab.tag.column.fieldRemark'),
      dataIndex: 'column_comment',
      key: 'column_comment',
      width: 180,
    },
    {
      title: t('classGrading.tab.tag.column.tableName'),
      dataIndex: 'table_name',
      key: 'table_name',
      width: 180,
    },
    {
      title: t('classGrading.tab.tag.column.tableRemark'),
      dataIndex: 'table_comment',
      key: 'table_comment',
      width: 180,
    },
    {
      title: t('classGrading.tab.tag.column.dataLength'),
      dataIndex: 'data_length',
      key: 'data_length',
      width: 150,
    },
    {
      title: t('classGrading.tab.tag.column.keyword'),
      dataIndex: 'keyword',
      key: 'keyword',
      width: 180,
    },
    {
      title: t('classGrading.tab.tag.column.notKeyword'),
      dataIndex: 'unkeyword',
      key: 'unkeyword',
      width: 180,
    },
    {
      title: t('classGrading.tab.tag.column.regular'),
      dataIndex: 'regular',
      key: 'regular',
      width: 180,
    },
    {
      title: t('classGrading.tab.tag.column.sensitive'),
      dataIndex: 'personal_bool',
      key: 'personal_bool',
      width: 180,
    },
    {
      title: t('classGrading.tab.tag.column.industry'),
      dataIndex: 'profession_bool',
      key: 'profession_bool',
      width: 180,
    },
    {
      title: t('classGrading.tab.tag.column.importantData'),
      dataIndex: 'important_data',
      key: 'important_data',
      width: 150,
    },
    {
      title: t('classGrading.tab.tag.column.coreData'),
      dataIndex: 'core_data',
      key: 'core_data',
      width: 150,
    },
    {
      title: t('classGrading.tab.tag.column.tagStatus'),
      dataIndex: 'label_status',
      key: 'label_status',
      width: 150,
      fixed: 'right',
      render: (val: 0 | 1, record: any) => (
        <Switch
          checked={val === 0 ? true : false} //0为启用，1为禁用
          onChange={(val: boolean) => onChangeTagStatus({ ids: `${record?.label_id}`, status: val })}
        />
      )
    },
    {
      title: t('common.text.action'),
      dataIndex: 'action',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_: any, record: any) => <Space>
        <Button type="link" size="small" onClick={() => onEdit(record)}>{t('common.btn.edit')}</Button>
        <Popconfirm
          title={t('common.modal.delete.content')}
          onConfirm={() => onDelete(record?.label_id)}
        >
          <Button type="link" size="small">{t('common.btn.delete')}</Button>
        </Popconfirm>
      </Space>
    },
  ]);
}