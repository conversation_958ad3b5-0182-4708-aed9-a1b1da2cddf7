import React, { useState } from 'react';
import { Form, Select, Upload, Button } from 'antd';
import type { UploadFile } from 'antd/es/upload/interface';
import type { UploadProps } from 'antd/es/upload';
import type { FormInstance } from 'antd/es/form';
import { Iconfont, UIModal } from 'src/components';
import { useRequest } from 'src/hook';
import {
  ClassStorageAPI,
  ClassTagDownloadExampleAPI
} from 'src/api'
import { FormLayout, FetchIndustryListParams } from '../../constants';
import { useTranslation } from 'react-i18next';

export default ({
  onCancel,
  onSubmit
}: {
  onCancel: () => void;
  onSubmit: (values: any) => void;
}) => {

  const { t } = useTranslation();
  const [importTemForm] = Form.useForm();

  const [fileList, setFileList] = useState<UploadFile[]>([]);

  const { run: runClassTagDownloadExampleAPI } = useRequest(ClassTagDownloadExampleAPI, {
    manual: true,
    onSuccess(res) {
      console.log('res', res);
    },
  })
  //行业类型
  const { data: industryTypeOptions } = useRequest(() =>
    ClassStorageAPI({ ...FetchIndustryListParams }),
    {
      formatResult(res: any) {
        const datas = res?.datas || [];
        const formattedTreeData = datas.map((item: any) => ({
          label: item?.name,
          value: item?.type,
        }))

        return formattedTreeData;
      },
    });
  const handleOk = async () => {
    importTemForm.validateFields().then((values) => {

      onSubmit({
        ...values,
        action: 'import_tag'
      })
    })
  };

  const onDownloadExample = (name: string) => {
    runClassTagDownloadExampleAPI({ fileName: name })
  }

  const uploadProps: UploadProps = {
    action: 'http:************:9898/user/classification/forward/cls_putfile',
    name: 'jUploaderFile',
    onRemove: (file) => {
      setFileList([]);
    },
    beforeUpload: (file) => {
      const filetype = file.name.split('')[0]; // 获取文件类型
      console.log(file, 'filetype==')
      return {
        ...file,
        data: { filetype }, // 动态设置 filetype
      };
    },
    onChange: ({ file }) => {
      console.log(file, 'info')
      setFileList([file]);
    },
    fileList,
    multiple: false
  };

  return (
    <UIModal
      title={t('common.btn.import')}
      visible={true}
      onOk={handleOk}
      onCancel={onCancel}
      confirmLoading={false}
      width={600}
      destroyOnClose
    >
      <Form form={importTemForm} {...FormLayout}>
        <Form.Item
          label={t('classGrading.tab.tag.industry.label')}
          name="type"
          rules={[{ required: true, message: t('classGrading.tab.tag.industry.plac') }]}
        >
          <Select options={industryTypeOptions} placeholder={t('classGrading.tab.tag.industry.plac')} allowClear />
        </Form.Item>

        <Form.Item
          label={t('classGrading.tab.tag.file.label')}
          name='file'
          rules={[{ required: true, message: t('classGrading.tab.tag.file.plac') }]}
        >
          <Upload {...uploadProps}>
            <Button type='primary'>{t('classGrading.tab.tag.file.btnText')}</Button>
          </Upload>
        </Form.Item>

        <Form.Item label={t('classGrading.tab.tag.identSample.label')}>
          <Button type='text' onClick={() => onDownloadExample('标识例样.xlsx')}>
            <Iconfont type='icon-wenjianjia' />{t('classGrading.tab.tag.identSample.label')}.xlsx
          </Button>
        </Form.Item>
        <Form.Item label={t('classGrading.tab.tag.indentTemplate.label')}>
          <Button type='text' onClick={() => onDownloadExample('标识模板.xlsx')}>
            <Iconfont type='icon-wenjianjia' />{t('classGrading.tab.tag.indentTemplate.label')}.xlsx
          </Button>
        </Form.Item>
      </Form>
    </UIModal>
  );
}
