import React, { useEffect } from "react";
import { Form, Input, Radio, Select, Row, Col } from 'antd';
import { useRequest } from 'src/hook';
import { ClassStorageAPI } from 'src/api';
import { UIModal } from 'src/components';
import { FetchIndustryListParams } from '../../constants';

export default ({
  editTagInfo,
  onCancel,
  onSubmit
}: {
  editTagInfo: any
  onCancel: () => void;
  onSubmit: (values: any) => void;
}) => {

  const [form] = Form.useForm();

  const { data: industryTypeOptions, run: runClassStorageAPI } = useRequest(ClassStorageAPI,
    {
      manual: true,
      formatResult(res: any) {
        const datas = res?.datas || [];
        const formattedTreeData = datas.map((item: any) => ({
          label: item?.name,
          value: item?.type,
        }))

        return formattedTreeData;
      },
    });

    useEffect(() => {
     form.setFieldsValue({...editTagInfo})
    },[editTagInfo])
  useEffect(() => {
    runClassStorageAPI({ ...FetchIndustryListParams });
  }, [])

  const fields = [
    {
      label: '数据长度',
      name: 'data_length',
      type: 'Input',
      valueName: 'length_weight', //对应权重
      placeholder: "例如:等于8,大于8,小于8",
    },
    {
      label: '数据类型',
      name: 'data_type',
      valueName: 'type_weight',
      type: 'Select',
      defaultValue: 'string',
      options: [{ label: '数字', value: 'number' },{ label: '字符串', value: 'string' }]
    },
    {
      label: '包含关键字',
      name: 'keyword',
      type: 'Input',
      valueName: 'keyword_weight',
      placeholder: "张三||李四，用'||'分隔",
    },
    {
      label: '不包含关键字',
      name: 'unkeyword',
      type: 'Input',
      valueName: 'unkeyword_weight',
      placeholder: "张三||李四，用'||'分隔",
    },
    {
      label: '正则表达式',
      name: 'regular',
      type: 'Input',
      valueName: 'regular_weight',
    },
    {
      label: '字段列名称',
      name: 'colname',
      valueName: 'colname_weight',
      type: 'Input',
      placeholder: "列1||列2，用'||'分隔"
    }, {
      label: '字段列备注',
      name: 'column_comment',
      valueName: 'column_comment_weight',
      placeholder: "列1||列2，用'||'分隔",
      type: 'Input',
    }, {
      label: '表名称',
      name: 'table_name',
      valueName: 'table_weight',
      type: 'Input',
      placeholder: "表1||表2，用'||'分隔",

    }, {
      label: '表备注',
      name: 'table_comment',
      valueName: 'table_comment_weight',
      type: 'Input',
      placeholder: "列1||列2，用'||'分隔",
    },

  ]
  const onValidateForm = () => {
    form.validateFields().then(values => {
      onSubmit({
        ...values,
        action: editTagInfo ? 'edit_tag' : 'add_tag',
      })
    }).catch(info => {
      console.log('Validate Failed:', info);
    });
  }
  return (
    <UIModal
      visible={true}
      title={editTagInfo ? "编辑标签" : "新增标签"}
      onCancel={onCancel}
      onOk={onValidateForm}
      bodyStyle={{ maxHeight: `calc(100vh - 290px)` }}
    >
      <Form form={form} colon={false} name="advanced-search-form" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
        <Row gutter={[20, 10]}>
          <Col span={12}>
            <Form.Item
              name="label_name"
              label="标识名称"
              rules={[{ required: true, message: '请输入标识名称' }]}
            >
              <Input placeholder="请输入标识名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="threshold"
              label="总匹配权重"
              rules={[{ required: true, message: '请输入总匹配权重' }]}
            >
              <Input placeholder="0.1~0.9" />
            </Form.Item>
          </Col>
          {
            fields.map(field => (
              <>
                <Col span={12} key={field.name}>
                  <Form.Item name={field.name} label={field.label} initialValue={field.defaultValue ?? ''}>
                    {
                      field.type === 'Input' ? (
                        <Input placeholder={field?.placeholder || `请输入${field?.label}`} />
                      ) : (
                        <Select placeholder={field.placeholder} options={field?.options || []} />
                      )
                    }
                  </Form.Item>
                </Col>
                <Col span={12} key={field.name}>
                  <Form.Item name={field.valueName} label={'识别权重'}
                    rules={[
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          if (value === undefined || (value >= 0.1 && value <= 0.9)) {
                            return Promise.resolve();
                          }
                          return Promise.reject(new Error('识别权重必须在0.1到0.9之间！'));
                        },
                      }),
                    ]}>
                    <Input placeholder="0.1~0.9" />
                  </Form.Item>
                </Col>
              </>
            ))
          }
          <Col span={12}>
            <Form.Item
              name="personal_bool"
              label="个人敏感信息"
              initialValue={false}
            >
              <Radio.Group buttonStyle="solid" >
                <Radio.Button value={1}>是</Radio.Button>
                <Radio.Button value={0}>否</Radio.Button>
              </Radio.Group>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="tableNote"
              label="行业敏感信息"
              initialValue={false}
            >
              <Radio.Group buttonStyle="solid">
                <Radio.Button value={1}>是</Radio.Button>
                <Radio.Button value={0}>否</Radio.Button>
              </Radio.Group>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="important_data"
              label="重要数据"
              initialValue={false}
            >
              <Radio.Group buttonStyle="solid">
                <Radio.Button value={1}>是</Radio.Button>
                <Radio.Button value={0}>否</Radio.Button>
              </Radio.Group>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="core_data"
              label="核心数据"
              initialValue={false}
            >
              <Radio.Group buttonStyle="solid">
                <Radio.Button value={1}>是</Radio.Button>
                <Radio.Button value={0}>否</Radio.Button>
              </Radio.Group>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              name="type"
              label="行业类型"
              labelCol={{ span: 3 }}
              wrapperCol={{ span: 20 }}
              rules={[{ required: true, message: '请选择行业类型' }]}
            >
              <Select options={industryTypeOptions} placeholder='请选择行业类型' />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </UIModal>
  )
}