import React, { useEffect, useState } from 'react';
import * as _ from 'lodash';
import { Table, Space, Button, Typography, Tooltip } from 'antd'
import i18n from 'i18next';
import { useTranslation } from 'react-i18next';
import type { ColumnsType } from 'antd/es/table';
import { useRequest, useSelector } from 'src/hook';
import {
  getClassBuiltTempTagList,
  getClassBuiltTempBindTag

} from 'src/api';
import {
  TagTransferModal
} from './modals';
import { getTablePaginatinLimitFiled } from '../utils';
import styles from './index.module.scss';

interface TagItem {
  key: string;
  name: string;
  level: string;
  category: string;
}

const TagManagementTable = ({
  setGradeConfigModalVisible
}: {
  setGradeConfigModalVisible: (visible: boolean) => void;
}) => {

  const { t } = useTranslation();
  const { selectedTemplateNodeInfo, activeTemplateTab } = useSelector(state => state.dataClassification);

  //标签
  const [tagModalVisible, setTagModalVisible] = useState<boolean>(false);

  //列表
  const {data: builtTempData, loading: tagListLoading, run: runGetClassBuiltTempTagList} = useRequest(getClassBuiltTempTagList
    ,{
    manual: true,
    formatResult: (res: any) => {
      return {
        total: res?.count?.total || 0,
        list: res?.datas ?? []
      }
    }
  });
  //绑定标签
  const { run: runBindTags } = useRequest(getClassBuiltTempBindTag, {
    manual: true,
    onSuccess: (res) => {
      setTagModalVisible(false);
    }
  });

  useEffect(() => {
    if (selectedTemplateNodeInfo?.tp_id) {
      runGetClassBuiltTempTagList({tp_id: selectedTemplateNodeInfo?.tp_id});

    }
  }, [selectedTemplateNodeInfo?.tip_id])


  return (
    <div className={styles.rightTemplateContent}>
      <div className={styles.header}>
        <div className={styles.title}>{selectedTemplateNodeInfo?.title}</div>
        <Space>
          {/* 最小分类才展示 */}
          {
            // selectedTemplateNodeInfo?.children?.length &&
            <>
              <Button type='primary' onClick={() => setTagModalVisible(true)}>{t('classGrading.tab.tag.tagName')}</Button>
              <Button type='primary' onClick={() => setGradeConfigModalVisible(true)}>{t('classGrading.tab.tag.gradeConfig')}</Button>
            </>
          }
        </Space>
      </div>
      <Table
        rowKey="key"
        columns={columns}
        loading={tagListLoading}
        dataSource={builtTempData?.list || []}
        scroll={{ y: `calc(100vh - 450px)` }}  //处理
        pagination={{
          showQuickJumper: true,
          showSizeChanger: true,
          total: builtTempData?.total || 0,
          showTotal: (total) => t('common.table.pagination.total', { total }),
        }}
      />

      {/* 标签 */}
      {
        tagModalVisible &&
        <TagTransferModal
          activeTemplateTab={activeTemplateTab}
          onCancel={() => { setTagModalVisible(false); }}
          onSubmit={(params: any) => runBindTags({...params, classify_id: selectedTemplateNodeInfo?.classify_id})}
        />
      }
    </div>
  );
};

export default TagManagementTable;

const columns: ColumnsType<TagItem> = [
  {
    title: i18n.t('classGrading.tab.tag.column.tagName'),
    dataIndex: 'label_names',
    key: 'label_names',
    width: '40%',
    ellipsis: true
  },
  {
    title: i18n.t('classGrading.tab.tag.level'),
    dataIndex: 'level',
    key: 'level',
    width: '20%',
    ellipsis: true
  },
  {
    title: i18n.t('classGrading.tab.tag.category'),
    dataIndex: 'classify_name',
    key: 'classify_name',
    width: '40%',
    ellipsis: true

  },
];