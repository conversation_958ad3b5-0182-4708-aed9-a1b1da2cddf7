.templateManagement {
  display: flex;
  flex-direction: row;
  height: calc(100vh - 186px);
  background-color: #fff;

  .resizableBox {
    margin-right: 10px;
    height: 100% !important;
  }

  .resizeHandle {
    position: absolute;
    right: -13px;
    top: calc(50% - 24px);
    font-size: 16px;
    cursor: col-resize;
    color: rgba(0, 0, 0, 0.85);
  }
}

.leftWrap,
.rightWrap {
  background-color: #f7f9fc;
  border-radius: 4px;
  height: 100%;
}

.leftWrap {
  width: 100%;
  padding: 16px 14px;
  border-radius: 4px;
  height: calc(100vh - 186px);

  .leftContainer {
    .title {
      font-size: 14px;
    }
  }
}

.rightWrap {
  flex: 1;
  width: 100%;
  padding: 16px;
  overflow: hidden;

  :global {
    .ant-tabs-nav-wrap {
      height: 56px;
      background-color: #fff;
      padding-left: 30px;
      padding-right: 30px;
      border-bottom: 1px solid #e5e5e5;
    }

    .ant-tabs-nav {
      margin-bottom: 0;
    }
  }

  .rightTemplateContent {
    padding: 0 20px 20px;
    background-color: #fff;
    height: 100%;
    min-height: 300px;

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #e5e5e5;
      margin-bottom: 20px;

      .title {

        height: 56px;
        padding: 12px 0px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        font-size: 18px;
      }
    }

  }

  .viewContent {
    padding: 10px 20px;
    height: calc(100% - 56px);
  }
}