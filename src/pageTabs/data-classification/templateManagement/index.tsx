import React, { useState } from "react";
import classnames from 'classnames';
import { ResizableBox, ResizableProps } from "react-resizable";
import { Tabs, message } from 'antd';
import { ErrorBoundary, Iconfont } from 'src/components';
import RightTemplateContent from './RightTemplateContent';
import { useSelector, useDispatch } from 'src/hook';
import { TEMPLATE_TABS } from '../constants';
import { TemplateTab } from '../types';
import { setActiveTemplateTab } from '../dataClassificationSlice'
import BuiltInTemplateTree from './leftTemplateTree/BuiltInTemplateTree';
import NewTemplateTree from './leftTemplateTree/NewTemplateTree';
import {
	AddOrEditTemplateModal,
	ImportTemplateModa,
	GradeConfigModal,
	AddOrEditClassificationModal
} from './modals';
import {
	getClassNewTemplateAPI,
	getClassAddCategory,
	getClassDelCategory,
	getClassEditCategory
} from 'src/api'
import styles from './index.module.scss';
import { useTranslation } from "react-i18next";

const ResizableBoxProps: ResizableProps = {
	axis: "x",
	width: 320,
	height: 0,
	minConstraints: [260, 0],
	maxConstraints: [600, 0],
};

export default () => {

	const dispatch = useDispatch();
	const { t } = useTranslation();
	const { activeTemplateTab, selectedTemplateNodeInfo } = useSelector(state => state.dataClassification);
	//刷新树节点
	const [needRefreshTree, setNeedRefreshTree] = useState<boolean>(false);
	//编辑信息
	const [addOrEditTemplateModalVisible, setAddOrEditModalVisible] = useState<boolean>(false);
	//通用 当前编辑节点信息
	const [curEditingDataInfo, setCurEditingDataInfo] = useState<any>(null);
	//导入模板
	const [importTemplateModalVisible, setImportTemplateModalVisible] = useState<boolean>(false);
	//等级配置
	const [gradeConfigModalVisible, setGradeConfigModalVisible] = useState<boolean>(false);
	//新增 编辑分类 
	const [addClassModalVisible, setAddClassModalVisible] = useState<boolean>(false);

	const ResizeHandle = (
		<div className={styles.resizeHandle}>
			<Iconfont type="icon-handle-8"></Iconfont>
		</div>
	);


	//新增分类

	const onSubmitCategory = (params: any) => {
		let action = getClassAddCategory;
		if (curEditingDataInfo) {
			action = getClassEditCategory
		}
		action(params).then(() => {
			message.success(t('common.message.addSuccess'))
			setAddClassModalVisible(false);
			setNeedRefreshTree(!needRefreshTree);
			setCurEditingDataInfo(null);
		})
	}
	const onAddOrEditClass = (node?: any) => {
		setAddClassModalVisible(true)
		if (node) {
			setCurEditingDataInfo(node);
		}
	}

	//新增模板
	const onSubmitAddOrEditTemplate = (params: any) => {
		getClassNewTemplateAPI({ ...params }).then(() => {
			if (curEditingDataInfo) {
				message.success(t('common.message.editSuccess'))

			} else {
				message.success(t('common.message.addSuccess'))
			}
			setAddOrEditModalVisible(false);
			setNeedRefreshTree(!needRefreshTree);
			setCurEditingDataInfo(null);
		})
	}

	const onAddOrEditTemplate = (node?: any) => {
		setAddOrEditModalVisible(true);
		if (node) {
			setCurEditingDataInfo(node);
		}
	}

	const onDeleteTreeNode = (params: any) => {
		//删除模板
		if (selectedTemplateNodeInfo?.isRoot) {
			getClassNewTemplateAPI(params).then(() => {
				message.success(t('common.message.delete_success'));
				setNeedRefreshTree(!needRefreshTree)
			})
		}else {
			//删除分类
			getClassDelCategory(params).then(() => {
				message.success(t('common.message.delete_success'));
				setNeedRefreshTree(!needRefreshTree)
			})
		}
		
	}

	//等级配置
	const onSettingGradeConfg = (node?: any) => {
		setGradeConfigModalVisible(true)
	}

	const onImportTemplate = () => {
		setImportTemplateModalVisible(true);
	}

	return (
		<div className={styles.templateManagement}>
			<ResizableBox
				className={styles.resizableBox}
				handle={ResizeHandle}
				{...ResizableBoxProps}
			>
				<div className={classnames(styles.leftWrap)}>
					<ErrorBoundary>
						<div className={styles.leftContainer}>
							<h4 className={styles.title}>{t('classGrading.tab.task.column.template')}</h4>
							<Tabs
								activeKey={activeTemplateTab}
								onChange={(key) => {
									dispatch(setActiveTemplateTab(key as TemplateTab));
								}}
							>
								{
									Object.keys(TEMPLATE_TABS).map((key) => (
										<Tabs.TabPane tab={TEMPLATE_TABS[key as TemplateTab]} key={key} />
									))
								}
							</Tabs>
							{/* 内置模板 */}
							{
								activeTemplateTab === 'builtInTemplate' && <BuiltInTemplateTree />
							}
							{/* 新增模板 */}
							{
								activeTemplateTab === 'newTemplate' &&
								<NewTemplateTree
									needRefreshTree={needRefreshTree}
									onAddOrEditClass={onAddOrEditClass}
									onAddOrEditTemplate={onAddOrEditTemplate}
									onDeleteTreeNode={onDeleteTreeNode}
									onSettingGradeConfg={onSettingGradeConfg}
									onImportTemplate={onImportTemplate}
								/>
							}
						</div>
					</ErrorBoundary>
				</div>
			</ResizableBox>
			<div className={styles.rightWrap}>
				<ErrorBoundary>
					<RightTemplateContent
						setGradeConfigModalVisible={setGradeConfigModalVisible}
					/>
				</ErrorBoundary>
			</div>
			{/* 新建 编辑 弹框 */}
			{
				addOrEditTemplateModalVisible &&
				<AddOrEditTemplateModal
					detailInfo={curEditingDataInfo}
					onCancel={() => { setAddOrEditModalVisible(false); setCurEditingDataInfo(null); }}
					onSubmit={(params: any) => onSubmitAddOrEditTemplate(params)}
				/>
			}
			{/* 导入模板 */}
			{
				importTemplateModalVisible &&
				<ImportTemplateModa
					onCancel={() => { setImportTemplateModalVisible(false); }}
				/>
			}
			{/* 等级配置 */}
			{
				gradeConfigModalVisible &&
				<GradeConfigModal
					onCancel={() => { setGradeConfigModalVisible(false); }}
				/>
			}
			{/* 新增 编辑分类 */}
			{
				addClassModalVisible &&
				<AddOrEditClassificationModal
					detailInfo={curEditingDataInfo}
					onCancel={() => { setAddClassModalVisible(false); setCurEditingDataInfo(null) }}
				  onSubmit={onSubmitCategory}
				/>
			}
		</div>
	)
}