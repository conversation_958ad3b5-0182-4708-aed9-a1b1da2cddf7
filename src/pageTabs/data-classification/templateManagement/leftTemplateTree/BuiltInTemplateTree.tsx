import React, { useEffect, useState, useMemo } from "react";
import { DeleteOutlined } from '@ant-design/icons'
import * as _ from "lodash";
import { DataNode } from "antd/lib/tree";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import { useLocation } from 'react-router-dom';
import { TreeNodeProps } from "rc-tree-select/lib/TreeNode";
import { Button, message, Modal, Tooltip, Tree, Spin } from "antd";
import { queryGroupNodes, queryTreeNode } from "src/api";
import { Iconfont } from "src/components";
import { useDispatch, useRequest, useSelector } from "src/hook";
import {
  getCurrentModulePermissionByUrl,
} from "src/util";
import {
  getClassBuiltTempAPI
} from 'src/api'
import {
  findNodeByKey,
  formatTreeData
} from './util';
import { setSelectedTemplateNodeInfo, } from "../../dataClassificationSlice";
import styles from "./index.module.scss";

interface IProps {
  [p: string]: any
}

const { DirectoryTree } = Tree;

const BuiltInTemplateTree = React.memo((props: IProps): JSX.Element => {

  const dispatch = useDispatch();
  const { t } = useTranslation();
  const location = useLocation();

  const {
    selectedNode
  } = useSelector((state) => state.dataProtection)

  const { permissionList } = useSelector((state) => state?.login);
  const { selectedTemplateNodeInfo } = useSelector(state => state.dataClassification);
  const [treeData, setTreeData] = useState<DataNode[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<any[]>([])
  const [selectedKeys, setSelectedKeys] = useState<any[]>([]);
  //所有点开过的节点 做缓存{path: []}
  const [treeNodeChildrenMap, setTreeNodeChildrenMap] = useState<any>({});

  const [treeHeight, setTreeHeight] = useState<number>(300)
  const [loadMoreLoading, setLoadMoreLoading] = useState<any>({})

  // 左侧treeData
  const { loading: rootLoading, run, refresh: refreshTree } = useRequest(getClassBuiltTempAPI, {
    manual: true,
    formatResult: (res: any) => {
      if (!res?.datas) return [];
      return formatTreeData(res.datas, true);
    },
    onSuccess: (res: any) => {
      setTreeData(res);
      setSelectedKeys([res?.[0]?.key ?? []]);
      const cloneNode = _.cloneDeep(res?.[0] ?? {});
      dispatch(setSelectedTemplateNodeInfo(cloneNode as any));
    }
  });

  useEffect(() => {
    queryTreeHeight();
    run({ action: 'query_list', limit: 1000000 });
    window.addEventListener('resize', queryTreeHeight)
    return () => {
      window.removeEventListener('resize', queryTreeHeight)
    }
  }, [])

  const queryTreeHeight = () => {
    const clientHeight = document.documentElement.clientHeight
    const treeHeight = clientHeight > 520 ? clientHeight - 300 : 330
    setTreeHeight(treeHeight)
  }

  // 默认选中
  useEffect(() => {

    const defaultSelectedItem = treeData && treeData.find((item: any) => item.nodeType === "datasource");
    if (!selectedKeys.length) {
      const cloneDefaultNode = _.cloneDeep(defaultSelectedItem);
      const defaultSelectedKeys = cloneDefaultNode?.key;
      dispatch(setSelectedTemplateNodeInfo(cloneDefaultNode as any));
      setSelectedKeys([defaultSelectedKeys]);
    }
  }, [treeData]);

  /**
   * 获取子节点
   */
  const { run: loadChildren, loading: childLoading } = useRequest(queryTreeNode, {
    manual: true,
    formatResult: res => {
      return formatTreeData(res);
    },
  });

  const fetchNodeChildren: any = async (node: TreeNodeProps, updatedTreeData?: any[], updateTreeNodeChildrenMap?: any) => {
    //不存在缓存 重新请求
    const props = node?.props;
    const key = node?.props?.nodePath;
    let cloneTreeData = _.cloneDeep(updatedTreeData || treeData);
    let cloneTreeNodeChildrenMap = _.cloneDeep(updateTreeNodeChildrenMap || treeNodeChildrenMap);

    const connectionId = props?.id ? props.id : props?.connectionId;
    const connectionType = props?.connection ? props?.connection?.connectionType : props?.connectionType;

    const params: any = {
      connectionId: connectionId,
      connectionType: connectionType,
      nodeType: props?.nodeType,
      nodeName: props?.nodeName,
      nodePath: props?.nodePath,
      nodePathWithType: props?.nodePathWithType,
    };
    // 根据父级参数标识给出默认分页配置
    if (node?.supportPaging) {
      params.pageNo = 1
      params.pageSize = 2000
    }
    // 根据当前节点属性设置分页配置
    if (node?.pageNo && node?.pageSize) {
      params.pageNo = node?.pageNo
      params.pageSize = node?.pageSize
    }
    const children = await loadChildren({
      ...params,
    });
    // 更新页码
    let loadMoreParams: any[] = []
    if (children?.length === params?.pageSize) {
      loadMoreParams = [{
        ...node,
        key: node?.key + '__loadMore',
        isLoadMore: true,
        isLeaf: true,
        hasChild: false,
        pageNo: (params?.pageNo ?? 1) + 1,
        pageSize: params?.pageSize
      }]
    }
    const target = findNodeByKey(cloneTreeData, key);
    if (target) {
      target.children = renderChildWithLoadMore(target?.children ?? [], children, loadMoreParams)
      cloneTreeNodeChildrenMap[key] = renderChildWithLoadMore(cloneTreeNodeChildrenMap[key] ?? [], children, loadMoreParams)
      return {
        newTreeData: cloneTreeData,
        newTreeNodeChildrenMap: cloneTreeNodeChildrenMap
      };
    }
  }

  const renderChildWithLoadMore = (originalData: any[], children: any[], loadMoreParams: any[]) => {
    const cloneOriginalData = _.cloneDeep(originalData)
    const filterChildren = cloneOriginalData.filter((item: any) => !item?.isLoadMore)
    const newChildren = [...filterChildren, ...children, ...loadMoreParams]
    return newChildren
  }

  const onLoadData = async (node: any) => {
    const { nodeType } = node.props;
    if (nodeType === "datasource" || nodeType === "group") {
      return new Promise<void>((resolve) => {
        resolve()
      })
    }
    let cloneTreeData = _.cloneDeep(treeData);
    let cloneTreeNodeChildrenMap = _.cloneDeep(treeNodeChildrenMap)
    try {
      const { newTreeData, newTreeNodeChildrenMap } = await fetchNodeChildren(node);
      cloneTreeData = newTreeData;
      cloneTreeNodeChildrenMap = newTreeNodeChildrenMap;
    } catch (err) {
      return new Promise<void>((resolve) => {
        resolve()
      })
    }
    setTreeData(cloneTreeData);
    setTreeNodeChildrenMap(cloneTreeNodeChildrenMap)
    return new Promise<void>((resolve) => {
      resolve()
    })
  }

  const onHandleDelete = (tp_id: any) => {
    if (!tp_id) return;
    Modal.confirm({
      centered: true,
      title: t('common.text.delete.tip'),
      onOk: async() => {
        getClassBuiltTempAPI({action: 'del', tp_id }).then(() => {
          message.success(t('common.message.delete_success'));
          refreshTree();
        })
      },
    });
  }

  const titleRender = (node: any) => {

    const { isRoot = false } = node;
    return (
      <div className={styles.treeTitleItem}>
        <div className={styles.titleTxtWrap}>
          <span className={styles.titleTxt}>{node?.title}</span>
        </div>
        {
          isRoot &&
          <Tooltip title={t('common.btn.delete')}>
            <Button type="link" icon={<DeleteOutlined />} onClick={() => { onHandleDelete(node?.tp_id) }}></Button>
          </Tooltip>
        }
      </div>
    )
  }

  return (
    <Spin spinning={rootLoading} wrapperClassName={styles.spinContainer}>
      <DirectoryTree
        showIcon={false}
        height={treeHeight}
        className={styles.builtInTemplateTree}
        titleRender={titleRender}
        treeData={treeData}
        loadData={onLoadData}
        expandAction={false}
        selectedKeys={selectedTemplateNodeInfo?.key ? [selectedTemplateNodeInfo?.key] : undefined}
        onSelect={(_key, { node }: any) => {

          setSelectedKeys([node?.key]);
          const cloneNode = _.cloneDeep(node);
          dispatch(setSelectedTemplateNodeInfo(cloneNode as any));

        }}
        expandedKeys={[...expandedKeys]}
        onExpand={(expandedKeys) =>
          setExpandedKeys(expandedKeys)
        }
      ></DirectoryTree>
      {!rootLoading && !treeData?.length && (
        <div className={styles.treePlaceholder}>{t('db.auth.noElement')}</div>
      )}
    </Spin>
  );
});

export default BuiltInTemplateTree;

