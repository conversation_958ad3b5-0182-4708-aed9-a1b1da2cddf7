import React, { useEffect, useState, useMemo } from "react";
import { MoreOutlined, PlusSquareOutlined } from '@ant-design/icons'
import * as _ from "lodash";
import { DataNode } from "antd/lib/tree";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import { useLocation } from 'react-router-dom';
import { TreeNodeProps } from "rc-tree-select/lib/TreeNode";
import { Dropdown, Menu, Modal, Tooltip, Tree, Space, Button, message, Spin } from "antd";
import { queryGroupNodes, queryTreeNode } from "src/api";
import { Iconfont } from "src/components";
import { getClassNewTemplateAPI } from 'src/api';
import { useDispatch, useRequest, useSelector } from "src/hook";
import {
  getCurrentModulePermissionByUrl,
} from "src/util";
import {
  findNodeByKey,
  generateTree,
  formatTreeData
} from './util';
import { NewTemplateOperation } from '../../types';
import { NEW_TEMPLATE_OPERATIONS } from '../../constants';
import { setSelectedTemplateNodeInfo, } from "../../dataClassificationSlice";
import styles from "./index.module.scss";

interface IProps {
  needRefreshTree: boolean
  onAddOrEditClass: (node?: any) => void;
  onAddOrEditTemplate: (node?: any) => void;
  onDeleteTreeNode: (params: any) => void;
  onSettingGradeConfg: (key: string) => void;
  onImportTemplate: () => void;
}

const { DirectoryTree } = Tree;

const NewTemplateTree = React.memo(({
  needRefreshTree,
  onAddOrEditClass,
  onAddOrEditTemplate,
  onDeleteTreeNode,
  onSettingGradeConfg,
  onImportTemplate
}: IProps): JSX.Element => {

  const dispatch = useDispatch();
  const { t } = useTranslation();
  const location = useLocation();

  const { selectedTemplateNodeInfo } = useSelector((state) => state.dataClassification)
 
  const { permissionList } = useSelector((state) => state?.login);

  const [treeData, setTreeData] = useState<DataNode[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<any[]>([])
  const [selectedKeys, setSelectedKeys] = useState<any[]>([]);
  //所有点开过的节点 做缓存{path: []}
  const [treeNodeChildrenMap, setTreeNodeChildrenMap] = useState<any>({});
  const [treeHeight, setTreeHeight] = useState<number>(300)

    // 左侧treeData
    const { loading: rootLoading, run } = useRequest(getClassNewTemplateAPI, { 
      manual: true,
      debounceInterval: 300,
      formatResult: (res: any) => {
        if (!res?.datas) return [];
        return formatTreeData(res.datas, true);
      },
      onSuccess:(res: any) => {
        setTreeData(res);
        setSelectedKeys([res?.[0]?.key ?? []]);
  
        const cloneNode = _.cloneDeep(res?.[0] ?? {});
        dispatch(setSelectedTemplateNodeInfo(cloneNode as any));
      }
    });

  const queryTreeHeight = () => {
    const clientHeight = document.documentElement.clientHeight
    const treeHeight = clientHeight > 520 ? clientHeight - 330 : 330
    setTreeHeight(treeHeight)
  }

  useEffect(() => {
    run({action: 'list_templates', limit: 1000000});
  }, [needRefreshTree]);

  useEffect(() => {
    queryTreeHeight();
   
    window.addEventListener('resize', queryTreeHeight)
    return () => {
      window.removeEventListener('resize', queryTreeHeight)
    }
  }, [])

  // 默认选中
  useEffect(() => {

    const defaultSelectedItem = treeData && treeData.find((item: any) => item.nodeType === "datasource");
    if (!selectedKeys.length) {
      const cloneDefaultNode = _.cloneDeep(defaultSelectedItem);
      const defaultSelectedKeys = cloneDefaultNode?.key;
      dispatch(setSelectedTemplateNodeInfo(cloneDefaultNode as any));
      setSelectedKeys([defaultSelectedKeys]);
    }
  }, [treeData]);

  /**
   * 获取子节点
   */
  const { run: loadChildren, loading: childLoading } = useRequest(queryTreeNode, {
    manual: true,
    formatResult: res => {
      return formatTreeData(res);
    },
  });

  const fetchNodeChildren: any = async (node: TreeNodeProps, updatedTreeData?: any[], updateTreeNodeChildrenMap?: any) => {
    //不存在缓存 重新请求
    const props = node?.props;
    const key = node?.props?.nodePath;
    let cloneTreeData = _.cloneDeep(updatedTreeData || treeData);
    let cloneTreeNodeChildrenMap = _.cloneDeep(updateTreeNodeChildrenMap || treeNodeChildrenMap);

    const connectionId = props?.id ? props.id : props?.connectionId;
    const connectionType = props?.connection ? props?.connection?.connectionType : props?.connectionType;

    const params: any = {
      connectionId: connectionId,
      connectionType: connectionType,
      nodeType: props?.nodeType,
      nodeName: props?.nodeName,
      nodePath: props?.nodePath,
      nodePathWithType: props?.nodePathWithType,
    };
    // 根据父级参数标识给出默认分页配置
    if (node?.supportPaging) {
      params.pageNo = 1
      params.pageSize = 2000
    }
    // 根据当前节点属性设置分页配置
    if (node?.pageNo && node?.pageSize) {
      params.pageNo = node?.pageNo
      params.pageSize = node?.pageSize
    }
    const children = await loadChildren({
      ...params,
    });
    // 更新页码
    let loadMoreParams: any[] = []
    if (children?.length === params?.pageSize) {
      loadMoreParams = [{
        ...node,
        key: node?.key + '__loadMore',
        isLoadMore: true,
        isLeaf: true,
        hasChild: false,
        pageNo: (params?.pageNo ?? 1) + 1,
        pageSize: params?.pageSize
      }]
    }
    const target = findNodeByKey(cloneTreeData, key);
    if (target) {
      target.children = renderChildWithLoadMore(target?.children ?? [], children, loadMoreParams)
      cloneTreeNodeChildrenMap[key] = renderChildWithLoadMore(cloneTreeNodeChildrenMap[key] ?? [], children, loadMoreParams)
      return {
        newTreeData: cloneTreeData,
        newTreeNodeChildrenMap: cloneTreeNodeChildrenMap
      };
    }
  }

  const renderChildWithLoadMore = (originalData: any[], children: any[], loadMoreParams: any[]) => {
    const cloneOriginalData = _.cloneDeep(originalData)
    const filterChildren = cloneOriginalData.filter((item: any) => !item?.isLoadMore)
    const newChildren = [...filterChildren, ...children, ...loadMoreParams]
    return newChildren
  }

  const onLoadData = async (node: any) => {
    const { nodeType } = node.props;
    if (nodeType === "datasource" || nodeType === "group") {
      return new Promise<void>((resolve) => {
        resolve()
      })
    }
    let cloneTreeData = _.cloneDeep(treeData);
    let cloneTreeNodeChildrenMap = _.cloneDeep(treeNodeChildrenMap)
    try {
      const { newTreeData, newTreeNodeChildrenMap } = await fetchNodeChildren(node);
      cloneTreeData = newTreeData;
      cloneTreeNodeChildrenMap = newTreeNodeChildrenMap;
    } catch (err) {
      return new Promise<void>((resolve) => {
        resolve()
      })
    }
    setTreeData(cloneTreeData);
    setTreeNodeChildrenMap(cloneTreeNodeChildrenMap)
    return new Promise<void>((resolve) => {
      resolve()
    })
  }

  const onDeleteConfirmModal = (node: any) => {

    Modal.confirm({
      centered: true,
      title: t('common.text.delete.tip'),
      onOk: () => {
        //区分删除分类 或删除模板
        let params: any = {
          action: 'delete_template', 
          id: node?.tp_id
        }
        if (!node?.isRoot) { //删除分类
          params = {
            label: node?.title,  //不确定
            id: node?.key,
          }
        }
        onDeleteTreeNode({});
      },
    });
  }

  const onHandleMoreOperations = (key: NewTemplateOperation, node?: any) => {
    switch (key) {
      case 'addClass':
        onAddOrEditClass();
        break;
      case 'addTemplate':
        onAddOrEditTemplate();
        break;
      case 'delete':
        //模板可直接删除
        if (node?.nodeType === 'datasource') {
          onDeleteConfirmModal(node);
          return;
        }
        
        //存在下层节点
        if (node?.children?.length) {
          return message.error(t('classGrading.tab.template.delete.tip'));
        } else {
          //已关联标签 不允许删除
          if (node?.isBind) {
            return message.error(t('classGrading.tab.template.delete.tip2'));
          }
          //最下层节点 且 未关联标签 允许删除
          onDeleteConfirmModal(node);
        }

        break;
      case 'gradeConfig':
        onSettingGradeConfg(node)
        break;
      case 'importTemplate':
        onImportTemplate();
        break;
      case 'edit':
        if (node?.nodeType === 'connection') {
          onAddOrEditClass(node)
        } else {
          onAddOrEditTemplate(node)
        }
        break;
      default:
        break;
    }
  }


  const treeMenu = (node: any) => {
    const { isRoot = false } = node;
    let operations = [];
    if (isRoot) {
      operations = ['addTemplate', 'addClass', 'edit', 'delete'];
    }else {
      operations = ['gradeConfig', 'addClass', 'edit', 'delete'];
      //未关联标签和新增分类
      if (!node?.isBind) {
        operations = operations.filter(operation => operation === 'addClass');
      }
    }

    return (
      <Menu
        className={styles.optionTxt}
        onClick={({ key }) => { onHandleMoreOperations(key as NewTemplateOperation, node) }}
      >
        {
          operations.map(operation => (
            <Menu.Item key={operation}>{NEW_TEMPLATE_OPERATIONS[operation as NewTemplateOperation]}</Menu.Item>
          ))
        }
      </Menu>
    )
  }
  const titleRender = (node: any) => {

    const { nodeType } = node;
    return (
      <div className={styles.treeTitleItem}>
        <div className={styles.titleTxtWrap}>
          <span className={styles.titleTxt}>{node?.title}</span>
        </div>
     
          <Dropdown overlay={() => treeMenu(node)}>
            <MoreOutlined className={classNames(styles.ml10, styles.options)} />
          </Dropdown>
      </div>
    )
  }

  return (
    <Spin spinning={rootLoading}>
      <Space className="flexAlignCenterJustifyEnd">
        <Tooltip title={t('classGrading.tab.template.tab.new')}>
          <Button
            type='link'
            icon={<PlusSquareOutlined />}
            onClick={() => { onHandleMoreOperations('addTemplate') }}
          />
        </Tooltip>
        <Tooltip title={t('classGrading.tab.template.action.importTemplate')}>
          <Button
            className='padding0'
            type='link'
            icon={<Iconfont type='icon-daorudaochu' />}
            onClick={() => { onHandleMoreOperations('importTemplate') }}
          />
        </Tooltip>
      </Space>

      <DirectoryTree
        showIcon={false}
        height={treeHeight}
        className={styles.newTemplateTree}
        titleRender={titleRender}
        treeData={treeData}
        loadData={onLoadData}
        expandAction={false}
        selectedKeys={ selectedTemplateNodeInfo?.key ? [selectedTemplateNodeInfo?.key] : undefined}
        onSelect={(_key, { node }: any) => {

          setSelectedKeys([node?.key]);
          const cloneNode = _.cloneDeep(node);
          dispatch(setSelectedTemplateNodeInfo(cloneNode as any));

        }}
        expandedKeys={[...expandedKeys]}
        onExpand={(expandedKeys) =>
          setExpandedKeys(expandedKeys)
        }
      ></DirectoryTree>
      {!rootLoading && !treeData?.length && (
        <div className={styles.treePlaceholder}>{t('db.auth.noElement')}</div>
      )}
    </Spin>
  );
});

export default NewTemplateTree;

