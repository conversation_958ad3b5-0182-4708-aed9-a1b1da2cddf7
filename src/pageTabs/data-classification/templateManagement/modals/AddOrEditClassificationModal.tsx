import React, { useEffect } from 'react';
import { Form, Input } from 'antd';
import { useTranslation } from 'react-i18next';
import type { FormInstance } from 'antd/es/form';
import { useRequest } from 'src/hook';
import { UIModal } from 'src/components';
import {
  getClassDatasourceAPI
} from 'src/api';
import { FormLayout } from '../../constants';

export default ({
  detailInfo,
  onCancel,
  onSubmit
}: {
  detailInfo: any;
  onCancel: () => void;
  onSubmit: (values: any) => void;
}) => {

  const { t } = useTranslation();
  const [classForm] = Form.useForm();

  useEffect(() => {
    if (!classForm) return;
    classForm.setFieldsValue({
     ...detailInfo
    })
  }, [classForm]);

  const onFormValidate = () => {
    classForm.validateFields().then((values) => {
      onSubmit({
        ...values,
        parentId: detailInfo?.tp_id
      })

    })
  };

  return (
    <UIModal
      title={detailInfo ? t('classGrading.tab.template.editClass') : t('classGrading.tab.template.addClass')}
      visible={true}
      onOk={onFormValidate}
      onCancel={onCancel}
      confirmLoading={false}
      width={600}
    >
      <Form form={classForm} {...FormLayout}>
        <Form.Item
          label={t('classGrading.tab.template.className.label')}
          name="label"
          rules={[{ required: true, message: t('classGrading.tab.template.className.plac') }]}
        >
          <Input placeholder={t('classGrading.tab.template.className.plac')} />
        </Form.Item>
      </Form>
    </UIModal>
  );
}
