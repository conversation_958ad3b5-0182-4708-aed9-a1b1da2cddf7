import React, { useEffect } from 'react';
import { Form, Select, message } from 'antd';
import { useTranslation } from 'react-i18next';
import { useRequest } from 'src/hook';
import { UIModal } from 'src/components';
import {
  getClassBindClsLevel,
  getClassDatasourceAPI
} from 'src/api';
interface TemplateFormValues {
  templateName: string;
  tagType?: string;
  relatedTemplate?: string;
}

export default ({
  onCancel
}: {
  onCancel: () => void;
}) => {

  const {t }= useTranslation();
  const [templateForm] = Form.useForm<TemplateFormValues>();

  //绑定分级
  const {loading ,run: runBindClsLevel } = useRequest(getClassBindClsLevel, {
    manual: true,
    onSuccess: () => {
      message.success('common.message.testSuccessfully')
      onCancel();
    }
  })
  const { data: levelOptions, run: runGetClassifyLevel } = useRequest(getClassDatasourceAPI,{
    manual: true,
    formatResult: (res) => {
      return [];
    }
  })

  useEffect(() => {
    runGetClassifyLevel({key: 'dd:data_dams_classify_level'})
  }, [])
  const onSubmit = () => {
    templateForm.validateFields().then((values) => {
      runBindClsLevel({
        ...values
      })

    })
  };

  return (
    <UIModal
      title={t('classGrading.tab.tag.gradeConfig')}
      visible={true}
      onOk={onSubmit}
      onCancel={onCancel}
      confirmLoading={loading}
      width={600}
    >
      <Form form={templateForm}>
        <Form.Item
          label={t('classGrading.tab.template.level.label')}
          name="templateName"
        >
          <Select options={levelOptions} placeholder={t('classGrading.tab.template.level.plac')} />
        </Form.Item>
      </Form>
    </UIModal>
  );
}
