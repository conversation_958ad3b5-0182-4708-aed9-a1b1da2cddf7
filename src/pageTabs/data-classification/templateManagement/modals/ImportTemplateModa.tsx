import React, {  useState } from 'react';
import { Form, Input, Select, Upload, Button } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import type { UploadFile } from 'antd/es/upload/interface';
import type { UploadProps } from 'antd/es/upload';
import type { FormInstance } from 'antd/es/form';
import { Iconfont, UIModal } from 'src/components';
import { useRequest } from 'src/hook';
import { FormLayout } from '../../constants';
import { useTranslation } from 'react-i18next';

export default ({
  onCancel
}: {
  onCancel: () => void;
}) => {

  const { t } = useTranslation();
  const [importTemForm] = Form.useForm();

  const [fileList, setFileList] = useState<UploadFile[]>([]);

  const { loading: submitBtnLoading, run: Update } = useRequest('', {

  })

  const handleOk = async () => {
    importTemForm.validateFields().then((values) => {


    })
  };

  const uploadProps: UploadProps = {
    beforeUpload: (file) => {
      setFileList([file]);
      return false;
    },
    onRemove: (file) => {
      setFileList([]);
    },
    fileList,
    multiple: false
  };


  // 模拟下拉选项 - 实际项目中替换为API获取
  const connectionMethodOptions = [
    { label: '数据源', value: 'datasource' },
    { label: '标签类型', value: 'tag' },
  ];

  const dataSourceOptions = [
    { label: 'MySQL', value: 'mysql' },
    { label: 'Oracle', value: 'oracle' },
  ];

  const tagTypeOptions = [
    { label: '敏感数据', value: 'sensitive' },
    { label: '普通数据', value: 'normal' },
  ];

  const templateOptions = [
    { label: 'P3分级模板', value: 'p3' },
    { label: 'P2分级模板', value: 'p2' },
  ];

  return (
    <UIModal
      title={t('common.btn.import')}
      visible={true}
      onOk={handleOk}
      onCancel={onCancel}
      confirmLoading={submitBtnLoading}
      width={600}
      destroyOnClose
    >
      <Form form={importTemForm} {...FormLayout}>
        <Form.Item
          label={t('classGrading.tab.template.templateName.label')}
          name="templateName"
          rules={[{ required: true, message: t('classGrading.tab.template.templateName.plac') }]}
        >
          <Input placeholder={t('classGrading.tab.template.templateName.plac')} />
        </Form.Item>
        <Form.Item
          label={t('classGrading.tab.template.connMethod.label')}
          name="connectionMethod"
          rules={[{ required: true, message: t('classGrading.tab.template.connMethod.plac') }]}
        >
          <Select options={connectionMethodOptions} placeholder={t('classGrading.tab.template.connMethod.plac')} allowClear />
        </Form.Item>

        <Form.Item noStyle dependencies={['connectionMethod']}>
          {
            ({ getFieldValue }) => {
              const isDatasource = getFieldValue('connectionMethod') === 'datasource';
              return isDatasource && (
                <Form.Item
                  label={t('classGrading.tab.task.column.dataSource')}
                  name="dataSource"
                  rules={[{ required: true, message: t('classGrading.tab.task.dataSource.plac') }]}
                >
                  <Select mode='multiple' options={dataSourceOptions} placeholder={t('classGrading.tab.task.dataSource.plac')} allowClear />
                </Form.Item>
              )
            }
          }
        </Form.Item>
        <Form.Item
          label={t('classGrading.tab.template.tagType.label')}
          name="tagType"
          rules={[{ required: true, message: t('classGrading.tab.template.tagType.plac') }]}
        >
          <Select options={tagTypeOptions} placeholder={t('classGrading.tab.template.tagType.plac')} allowClear />
        </Form.Item>

        <Form.Item
          label={t('classGrading.tab.template.relatedTemplate.label')}
          name="relatedTemplate"
          rules={[{ required: true, message: t('classGrading.tab.template.relatedTemplate.plac') }]}
        >
          <Select options={templateOptions} placeholder={t('classGrading.tab.template.relatedTemplate.plac')} allowClear />
        </Form.Item>

        <Form.Item
          label={t('common.downloadTemplate.fileUpload.upload')}
          name='file'
          rules={[{ required: true, message: t('classGrading.tab.tag.file.plac') }]}
        >
          <Upload {...uploadProps}>
            <Button type='primary' icon={<UploadOutlined />}>{t('classGrading.tab.tag.file.btnText')}</Button>
          </Upload>
        </Form.Item>

        <Form.Item label={t('classGrading.tab.template.action.importTemplate')}>
          <div><Iconfont type='icon-wenjianjia' className='ml10' />{t('classGrading.tab.template.asscoTag.label')}.xlsx</div>
        </Form.Item>

        <Form.Item label={t('classGrading.tab.template.action.importTemplate')}>
          <div><Iconfont type='icon-wenjianjia' className='ml10' />{t('classGrading.tab.template.asscoDatasouce.label')}.xlsx</div>
        </Form.Item>
      </Form>
    </UIModal>
  );
}
