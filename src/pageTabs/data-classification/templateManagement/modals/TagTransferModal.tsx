import React, { useState, useEffect } from 'react';
import { Tree, Button, Input } from 'antd';
import { useTranslation } from 'react-i18next';
import { DeleteOutlined, ArrowRightOutlined } from '@ant-design/icons';
import { useRequest } from 'src/hook';
import { UIModal } from 'src/components';
import { filterNodesNotMatch } from 'src/util';
import {
  getClassDatasourceAPI
} from 'src/api';
import { TemplateTab } from '../../types';
import styles from './index.module.scss';

interface TreeNode {
  key: string;
  title: string;
  children?: TreeNode[];
}

export default ({
  activeTemplateTab,
  onCancel,
  onSubmit
}: {
  activeTemplateTab: TemplateTab,
  onCancel: () => void;
  onSubmit: (data: any) => void;
}) => {
  const treeData = [
    {
      title: '父节点 1',
      key: '0-0',
      name: '父节点 1',
      children: [
        {
          title: '子节点 1-1',
          key: '0-0-0',
          name: '子节点 1-1',
        },
        {
          title: '子节点 1-2',
          key: '0-0-1',
          name: '子节点 1-2',
        },
      ],
    },
    {
      title: '父节点 2',
      key: '0-1',
      name: '父节点 2',
      children: [
        {
          title: '子节点 2-1',
          key: '0-1-0',
          name: '子节点 2-1',
        },
        {
          title: '子节点 2-2',
          key: '0-1-1',
          name: '子节点 2-2',
        },
      ],
    },
  ];
//不同模板接口可能不一致， 需要特殊处理
  const { t } = useTranslation();
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]); // 左侧 Tree 选中的节点
  const [listData, setListData] = useState<TreeNode[]>([]); // 右侧 List 展示的数据
  const [searchValue, setSearchValue] = useState<string | null>(null); // 搜索框的值
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]); // 左侧 Tree 展开的节点
  const [autoExpandParent, setAutoExpandParent] = useState(false); // 自动展开父节点
  const [filteredTreeData, setFilteredTreeData] = useState(treeData);


  const {data: tagList, run: runGetTemplateTags } = useRequest(getClassDatasourceAPI, {
    manual: true,
    formatResult(res) {
      return res
    },
  })

  useEffect(() => {
    runGetTemplateTags({ key:'dd:dams_label_type' });
  }, [activeTemplateTab])
  // 判断节点是否为叶子节点
  const isLeafNode = (node: any) => {
    return !node.children || node.children.length === 0;
  };

  // 递归查找叶子节点的 key
  const getLeafKeys = (node: any) => {
    const leafKeys: React.Key[] = [];
    if (isLeafNode(node)) {
      leafKeys.push(node.key);
    } else {
      node.children.forEach((child: any) => {
        leafKeys.push(...getLeafKeys(child));
      });
    }
    return leafKeys;
  };

  const handleTreeSelect = (keys: any, info: any) => {
    // 仅处理子节点的选中状态
    const selectedNode = info.node;
    const leafKeys = getLeafKeys(selectedNode); 
  
    let newSelectedKeys = [...selectedKeys];
    if (info.checked) {
      newSelectedKeys = [...newSelectedKeys, ...leafKeys];
    } else {
      newSelectedKeys = newSelectedKeys.filter((key) => !leafKeys.includes(key));
    }
    setSelectedKeys(newSelectedKeys);
  };

  // 处理箭头按钮点击事件
  const handleArrowClick = () => {
    const newListData: TreeNode[] = [...listData];
    const processedKeys = new Set(); // 用于记录已处理的节点 key

    // 遍历选中的节点
    selectedKeys.forEach((key) => {
      const node = findNodeByKey(filteredTreeData, key);
      if (node && !processedKeys.has(key)) {
        // 如果是父节点，仅添加其子节点
        if (node?.children) {
          node.children.forEach((child: any) => {
            if (!processedKeys.has(child.key)) {
              newListData.push(child);
              processedKeys.add(child.key);
            }
          });
        } else {
          // 如果是子节点，直接添加
          newListData.push(node);
          processedKeys.add(key);
        }
      }
    });
    const uniqueArray = newListData.reduce((acc: any[], item: any) => {
      const isDuplicate = acc.some((obj: any) => obj.key === item.key);
      if (!isDuplicate) {
        acc.push(item);
      }
      return acc;
    }, []);
    setListData(uniqueArray);
  };

  // 删除事件
  const handleDelete = (key: React.Key) => {
    // 过滤掉被删除的节点
    const newListData = listData.filter((item) => item.key !== key);
    setListData(newListData);
    // 更新左侧 Tree 的选中状态
    let newSelectedKeys = selectedKeys.filter((selectedKey) => selectedKey !== key);
    // 移除父节点的 key
    const parent = findParentByKey(treeData, key);
    if (parent) {
      newSelectedKeys = newSelectedKeys.filter((selectedKey) => selectedKey !== parent.key);
    }
  
    setSelectedKeys(newSelectedKeys);
  };

  // 根据 key 查找节点
  const findNodeByKey = (data: TreeNode[], key: React.Key): any => {
    for (const node of data) {
      if (node.key === key) {
        return node;
      }
      if (node?.children?.length) {
        const result: TreeNode | null = findNodeByKey(node.children, key);
        if (result) {
          return result;
        }
      }
    }
    return null;
  };

  // 递归查找父节点
  const findParentByKey = (data: TreeNode[], key: React.Key): any => {
    for (const node of data) {
      if (node?.children?.length) {
        const child = node.children.find((child) => child.key === key);
        if (child) {
          return node;
        }
        const result: TreeNode | null = findParentByKey(node.children, key);
        if (result) {
          return result;
        }
      }
    }
    return null;
  };

  useEffect(() => {

    setFilteredTreeData(searchValue ? filterNodesNotMatch(treeData, searchValue) : treeData);

  }, [searchValue, JSON.stringify(treeData)]);


  function getExpandKeysNodeTreeAllChildren(treeNode: any) {
    const keys: string[] = []

    const getKeys = (node: any) => {
      if (node?.children?.length) keys.push(node.key)
      if (node?.children?.length) {
        node.children.forEach((child: any) => getKeys(child))
      }
    }
    getKeys(treeNode)

    return keys
  }

  useEffect(() => {
    if (!searchValue) {
      // 删除搜索内容后，保留选中的信息
      const newSelectedKeys = selectedKeys.filter((key) => {
        const node = findNodeByKey(treeData, key);
        return node !== null;
      });
      setSelectedKeys(newSelectedKeys);
    }
  }, [searchValue, JSON.stringify(treeData)]);

  useEffect(() => {

    if (searchValue && filteredTreeData) {
      let keys: string[] = [];

      filteredTreeData?.forEach((item: any) => {
        keys = keys.concat(getExpandKeysNodeTreeAllChildren(item))
      })

      setExpandedKeys(keys)
    }

  }, [searchValue, JSON.stringify(filteredTreeData)])

  const handleExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys)
    setAutoExpandParent(false)
  }
  
  const onSubmitSelectedTags = () => {
     const keys = listData.map((item: any) => item.title);
     onSubmit({labels: keys})
  }


  return (
    <UIModal
      visible={true}
      title={t('classGrading.tab.tag.tagName')}
      onCancel={onCancel}
      onOk={onSubmitSelectedTags}
      className={styles.tagModal}
    >
      <div className='flexAlignCenter'>
        <div className={styles.leftTree}>
          <Input placeholder={t('common.search.input.placeholder')} onChange={(e) => setSearchValue(e.target.value)} />
          <Tree.DirectoryTree
            checkable
            expandedKeys={expandedKeys}
            autoExpandParent={autoExpandParent}
            checkStrictly={false}
            onCheck={handleTreeSelect}
            onExpand={handleExpand}
            treeData={filteredTreeData}
            checkedKeys={selectedKeys}
            style={{ width: '300px' }}
          />
        </div>
        {/* 中间箭头按钮 */}
        <Button
          shape="circle"
          icon={<ArrowRightOutlined />}
          className='ml10 mr10'
          onClick={handleArrowClick}
        />
        {/* 右侧 List */}
        <div className={styles.rightList}>
          {
            listData?.map(item => (
              <div className={styles.item}>
                <div>{item?.title}</div>
                <Button
                  type='text'
                  icon={<DeleteOutlined className={styles.deleteIcon} />}
                  onClick={() => handleDelete(item.key)}
                />
              </div>
            ))
          }
        </div>
      </div>
    </UIModal>
  );
};
