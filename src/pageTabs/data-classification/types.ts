
// 定义单个类型，只能是 PERM_PERIOD_PARAMETER_UNIT 中的值之一
export type ClassificationTab = 'templateManagement' | 'tagManagement' | 'classificationTask' | 'classificationResult';

//模板管理 -新增模板操作
export type NewTemplateOperation = 'addClass' | 'delete' | 'edit' | 'addTemplate' | 'gradeConfig' | 'importTemplate';
//标签
export type IClassificationTreeOperation = 'add' | 'delete' | 'edit';

// 定义 模板管理 Tabs 类型
export type TemplateTab = 'builtInTemplate' | 'newTemplate';

//分类分级任务 -调度周期
export type TaskSheduleCycle = 10000 | 1 | 2 | 3 | 7 | 14 | 21;

//分类分级任务 -枚举任务执行状态
export type ExecutionStatus = 0 | 1 | 2 | 3 | 9 | 'success' | 'failed';

//分级结果-筛选条件
export type ClassificationRes = 'ALL' | 'GRADED' | 'UNGRADED';
