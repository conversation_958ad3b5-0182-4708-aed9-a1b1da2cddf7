import React, { useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useDispatch, useSelector } from 'src/hook'
import { Form, Input, message, TreeSelect, Checkbox } from 'antd'
import { UIModal } from 'src/components'
import { hideModal } from 'src/store/extraSlice/modalVisibleSlice'
import { fetchOrgTreeData } from '../organizationSlice'
import { addDept, OrgTreeNode } from 'src/api'
import { FormLayout } from 'src/constants'

const traversalOrgTreeNode = (rootNode: OrgTreeNode): any => {
  const { title, name, orgType, children } = rootNode
  return {
    title,
    value: name,
    selectable: ['COMPANY', 'DEPT'].includes(orgType),
    children: (children || []).map(traversalOrgTreeNode),
  }
}

export const AddDeptModal = () => {
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false)

  const dispatch = useDispatch()
  const { t } = useTranslation()
  
  const { selectedNode, orgTreeData } = useSelector(
    (state) => state.organization,
  )
  const isLangEn = useSelector(state => state.login.locales) === 'en'

  const initialSelectedParent = useMemo(() => {
    if (selectedNode?.orgType === 'DEPT') {
      return selectedNode?.name
    } else {
      return orgTreeData[0]?.name
    }
  }, [selectedNode, orgTreeData])

  const treeSelectData = useMemo(() => {
    return orgTreeData.map(traversalOrgTreeNode)
  }, [orgTreeData])

  const { AddDept } = useSelector((state) => state.modal)
  const visible = AddDept?.visible || false
  const [addDeptForm] = Form.useForm()

  const handleSubmitAddDept = () => {
    if (!addDeptForm) {
      console.error('表单实例未正确初始化');
      return;
    }
    addDeptForm.validateFields().then((values: any) => {
      const { name, description, principal } = values
  
      let parId: any = null
      if (
        selectedNode?.orgType === 'DEPT' ||
        selectedNode?.orgType === 'SUB_COMPANY' ||
        selectedNode?.orgType === 'COMPANY'
      ) {
        parId = selectedNode?.id
      }
      const parents = [parId]

      setConfirmLoading(true)
      addDept({ name, description, parents, principal, orgType:'DEPT'})
        .then(() => {
          message.success(t('systemManagement.personManagement.addDept.success'))
          dispatch(fetchOrgTreeData())
          dispatch(hideModal('AddDept'))
        })
        .finally(() => setConfirmLoading(false))
    })
  }

  const curOrgType = selectedNode?.orgType || orgTreeData[0]?.orgType
  const modalLabel = curOrgType === 'DEPT' ? t('systemManagement.personManagement.table.column.departmentName') : t('systemManagement.personManagement.addDept.subordinateCompany');

  return (
    <UIModal
      title={t('systemManagement.personManagement.addEdpt')}
      visible={visible}
      onCancel={() => dispatch(hideModal('AddDept'))}
      onOk={() => handleSubmitAddDept()}
      width={520}
      confirmLoading={confirmLoading}
    >
      {/* todo 全局统一： Modal 组合 Form 时设置 Form preserve: false，而不是 Modal afterClose 重置表单 */}
      <Form form={addDeptForm} {...FormLayout} labelCol={{span: isLangEn ? 10 : 8}}  preserve={false}>
        <Form.Item
          name="parent"
          label={modalLabel}
          initialValue={initialSelectedParent}
        >
          <TreeSelect treeData={treeSelectData} treeDefaultExpandAll disabled />
        </Form.Item>
        <Form.Item
          name="name"
          label={t('systemManagement.personManagement.deptName')}
          rules={[
            { required: true, message: t('systemManagement.personManagement.deptName.plac') },
            { max: 20, min: 2, message: t('systemManagement.personManagement.deptName.hint') },
            { pattern: /^[a-zA-Z0-9\u4e00-\u9fa5]+$/, message: t('systemManagement.personManagement.deptName.hint2') },
          ]}
        >
          <Input></Input>
        </Form.Item>
      </Form>
    </UIModal>
  )
}
