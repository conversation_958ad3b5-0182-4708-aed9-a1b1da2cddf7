import React, { useMemo, useState } from 'react'
import  { useTranslation } from 'react-i18next'
import { useDispatch, useSelector } from 'src/hook'
import { Form, Input, message, TreeSelect } from 'antd'
import { UIModal } from 'src/components'
import { hideModal } from 'src/store/extraSlice/modalVisibleSlice'
import { fetchOrgTreeData } from '../organizationSlice'
import { OrgTreeNode, addOrgGroup } from 'src/api'
import { FormLayout } from 'src/constants'
import { nameValidator } from 'src/util/nameValidator'

const traversalOrgTreeNode = (rootNode: OrgTreeNode): any => {
  const { title, name, orgType, children } = rootNode
  return {
    title,
    value: name,
    selectable: ['COMPANY', 'DEPT'].includes(orgType),
    children: (children || []).map(traversalOrgTreeNode),
  }
}

export const AddOrgGroupModal: React.FC = () => {
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false)

  const dispatch = useDispatch()
  const { t } = useTranslation()
  const visible = useSelector((state) => state.modal.AddOrgGroup?.visible || false)
  const isLangEn = useSelector(state => state.login.locales) === 'en'

  const { selectedNode, orgTreeData } = useSelector(
    (state) => state.organization,
  )
  const treeSelectData = useMemo(() => {
    return orgTreeData.map(traversalOrgTreeNode)
  }, [orgTreeData])
  const initialSelectedParent = useMemo(() => {
    if (selectedNode?.orgType === 'DEPT') {
      return selectedNode.name
    } else {
      return orgTreeData[0]?.name
    }
  }, [selectedNode, orgTreeData])

  const [addOrgGroupForm] = Form.useForm()

  const handleSubmitAddOrgGroup = () => {
    addOrgGroupForm.validateFields().then((values) => {
      const { name, description, parent } = values
      let parId: any = null
      if (selectedNode?.orgType === 'DEPT') {
        parId = selectedNode?.id
      } 
      const parentOrg = parId === orgTreeData[0]?.id ? undefined : parId

      setConfirmLoading(true)
      addOrgGroup({ name, description, parentOrg })
        .then(() => {
          message.success(t('systemManagement.personManagement.addGroup.success'))
          dispatch(fetchOrgTreeData())
          dispatch(hideModal('AddOrgGroup'))
        })
        .finally(() => setConfirmLoading(false))
    })
  }

  return (
    <UIModal
      title={t('systemManagement.personManagement.addGroup')}
      visible={visible}
      onCancel={() => dispatch(hideModal('AddOrgGroup'))}
      onOk={() => handleSubmitAddOrgGroup()}
      afterClose={() => addOrgGroupForm.resetFields()}
      width={520}
      confirmLoading={confirmLoading}
    >
      <Form form={addOrgGroupForm} {...FormLayout} labelCol={{ span: isLangEn  ? 11 : 8 }}>
        <Form.Item
          name="parent"
          label={t('systemManagement.personManagement.table.column.departmentName')}
          initialValue={initialSelectedParent}
        >
          <TreeSelect treeData={treeSelectData} treeDefaultExpandAll disabled />
        </Form.Item>
        <Form.Item
          name="name"
          label={t('systemManagement.personManagement.groupName')}
          rules={[
            { required: true, message: t('systemManagement.personManagement.groupName.plac') },
            { max: 100, min: 2, message: t('systemManagement.personManagement.groupName.hint') },
            { validator: nameValidator },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item name="description" label={t('systemManagement.personManagement.desc')}>
          <Input />
        </Form.Item>
      </Form>
    </UIModal>
  )
}
