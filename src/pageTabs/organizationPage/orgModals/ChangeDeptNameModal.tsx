import React, { useEffect, useRef } from 'react'
import { useSelector, useDispatch, useRequest } from 'src/hook'
import { Form, Input, message } from 'antd'
import { useTranslation } from 'react-i18next'
import { UIModal } from 'src/components'
import { fetchOrgTreeData } from '../organizationSlice'
import { hideModal } from 'src/store/extraSlice/modalVisibleSlice'
import { editDept } from "src/api";

export const ModalChangeDeptName = () => {
  const dispatch = useDispatch()
  const { t } = useTranslation()
  const visible = useSelector((state) => state.modal.ModalChangeDeptName?.visible || false);
  const selectedNode = useSelector((state) => state.organization.selectedNode);
  const { loading, run } = useRequest(editDept, {
    manual: true,
    onSuccess: () => {
      message.success(t('common.message.editSuccess'));
      dispatch(fetchOrgTreeData());
      dispatch(hideModal("ModalChangeDeptName"));
    },
  });

  const inputRef = useRef<Input>(null)
  const [form] = Form.useForm()

  useEffect(() => {
    if (visible) {
      form.setFieldsValue({name: selectedNode?.name})
      inputRef.current?.focus()
    }
  }, [visible, selectedNode, form])

  const modalTitle = selectedNode?.orgType === 'DEPT' ? t('systemManagement.personManagement.editDeptName') : t('systemManagement.personManagement.editPartnerName');
  const modalInput = selectedNode?.orgType === 'DEPT' ? t('systemManagement.personManagement.deptName') : t('systemManagement.personManagement.partnerName');

  return (
    <UIModal
      title={modalTitle}
      visible={visible}
      onCancel={() => {
        dispatch(hideModal("ModalChangeDeptName"));
      }}
      onOk={() => {
        form.validateFields().then(({ name }) => {
          const params = {
            name,
            id: selectedNode?.id
          }
          run(params)
        })
      }}
      confirmLoading={loading}
      width={320}
    >
      <Form form={form}>
        <Form.Item
          name="name"
          rules={[
            { required: true, message: t('systemManagement.personManagement.deptName.plac') },
            // { pattern: /^[a-zA-Z0-9\u4e00-\u9fa5]+$/, message: t('systemManagement.personManagement.deptName.hint2') },
            { max: 100, message: t('systemManagement.personManagement.name.hint') },
          ]}
        >
          <Input ref={inputRef} placeholder={modalInput} />
        </Form.Item>
      </Form>
    </UIModal>
  )
}
