import { useState, useRef, useCallback, useEffect } from 'react'
import type { Grid<PERSON>pi, ColumnApi } from '@ag-grid-community/core'
import {
  createMatchPattern,
  performRowMatching,
  type MatchOptions as UtilMatchOptions,
  type MatchPattern
} from '../utils/matchUtils'

export interface SearchOptions extends UtilMatchOptions {
  useQuickFilter: boolean
}

export interface MatchLocation {
  rowNode: any
  colId: string
}

export interface UseSearchProps {
  gridApiRef: React.MutableRefObject<{
    api: GridApi
    columnApi: ColumnApi
  } | null>
}

export interface UseSearchReturn {
  // 搜索状态
  searchVisible: boolean
  currentSearchText: string
  currentSearchOptions: SearchOptions
  allMatchLocations: MatchLocation[]
  currentMatchIndex: number
  totalMatches: number
  
  currentSearchTextRef: React.MutableRefObject<string>
  currentSearchOptionsRef: React.MutableRefObject<SearchOptions>
  needFullUpdateRef: React.MutableRefObject<boolean>
  hasSearchExecutedRef: React.MutableRefObject<boolean> // 标记是否已经执行过搜索

  // 搜索操作
  setSearchVisible: (visible: boolean) => void
  handleSearchNavigation: (direction: 'next' | 'prev') => void
  handleQuickFilter: (searchText: string) => void
  handleSearch: (searchText: string, options: SearchOptions) => void
  retriggerSearchAfterPagination: () => void

  // 高亮管理
  clearAllHighlights: () => void
  addHighlightToCell: (rowIndex: number, colId: string) => void
  
  // 全量更新标识
  needFullUpdate: boolean
  setNeedFullUpdate: (need: boolean) => void

  
  // 优化的高亮数据结构 (用于 cellClassRules)
  searchHighlightRefs: {
    matchedCellsSetRef: React.MutableRefObject<Set<string>>
    currentFocusCellKeyRef: React.MutableRefObject<string | null>
  }
}

export function useSearch({ gridApiRef }: UseSearchProps): UseSearchReturn {
  // 搜索状态
  const [searchVisible, setSearchVisible] = useState(false)
  const [currentSearchText, setCurrentSearchText] = useState('')
  const [currentSearchOptions, setCurrentSearchOptions] = useState<SearchOptions>({
    caseSensitive: false,
    useRegex: false,
    useQuickFilter: false
  })
  const [allMatchLocations, setAllMatchLocations] = useState<MatchLocation[]>([])
  const [currentMatchIndex, setCurrentMatchIndex] = useState(-1)
  const [totalMatches, setTotalMatches] = useState(0)
  
  // 全量更新标识
  const [needFullUpdate, setNeedFullUpdate] = useState(false)
  
  // 待处理搜索状态 - 用于事件驱动的搜索执行
  const [pendingSearch, setPendingSearch] = useState<{ text: string; options: SearchOptions } | null>(null)
  
  // 搜索相关 ref
  const currentSearchTextRef = useRef<string>(currentSearchText)
  const currentSearchOptionsRef = useRef<SearchOptions>(currentSearchOptions)
  const needFullUpdateRef = useRef<boolean>(false)
  const processedRowNodeIdsRef = useRef<Set<string>>(new Set())
  const hasSearchExecutedRef = useRef<boolean>(false) // 标记是否已经执行过搜索
  
  // 高亮管理状态
  const matchedCellsSetRef = useRef<Set<string>>(new Set())
  const currentFocusCellKeyRef = useRef<string | null>(null)

  // 监听搜索结果变化，更新高亮数据结构
  useEffect(() => {
    const newSet = new Set<string>()
    allMatchLocations.forEach(match => {
      if (match.rowNode && typeof match.rowNode.id !== 'undefined') {
        newSet.add(`${match.rowNode.id}_${match.colId}`)
      }
    })
    matchedCellsSetRef.current = newSet


    if (currentMatchIndex !== -1 && allMatchLocations[currentMatchIndex]) {
      const currentMatch = allMatchLocations[currentMatchIndex]
      if (currentMatch.rowNode && typeof currentMatch.rowNode.id !== 'undefined') {
        currentFocusCellKeyRef.current = `${currentMatch.rowNode.id}_${currentMatch.colId}`
      } else {
        currentFocusCellKeyRef.current = null
      }
    } else {
      currentFocusCellKeyRef.current = null
    }

    // 当搜索结果或焦点变化时，通知 AG Grid 刷新单元格
    if (gridApiRef.current?.api) {
      gridApiRef.current.api.refreshCells({ force: true, suppressFlash: true })
    }
  }, [allMatchLocations, gridApiRef, currentMatchIndex])

  // 通用的行匹配函数
  const performCellMatching = useCallback((
    rowNode: any,
    matchPattern: MatchPattern,
    orderedColIds?: string[]
  ): string[] => {
    if (!rowNode?.data || !matchPattern) {
      return []
    }

    return performRowMatching(rowNode.data, matchPattern, orderedColIds)
  }, [])


  const clearAllHighlights = useCallback(() => {
    // 清空匹配数据，触发 useEffect 更新
    matchedCellsSetRef.current = new Set()
    currentFocusCellKeyRef.current = null
    if (gridApiRef.current?.api) {
      gridApiRef.current.api.refreshCells({ force: true, suppressFlash: true })
    }
  }, [gridApiRef])

  // 添加高亮到指定单元格 - 现在通过 cellClassRules 自动处理
  const addHighlightToCell = useCallback((rowIndex: number, colId: string) => {
    // 这个函数现在主要用于兼容，实际高亮通过 cellClassRules 处理
    // 只需要确保 AG Grid 的焦点设置
    if (!gridApiRef.current) return
    
    const { api } = gridApiRef.current
    api.ensureIndexVisible(rowIndex, 'middle')
    api.ensureColumnVisible(colId)
    api.setFocusedCell(rowIndex, colId)
  }, [gridApiRef])

  // 封装的核心搜索逻辑
  const executeSearchLogic = useCallback((
    searchText: string,
    options: SearchOptions
  ) => {
    if (!gridApiRef.current) {
      return
    }
    
    const { api, columnApi } = gridApiRef.current
    
    // 重置状态
    setAllMatchLocations([])
    setTotalMatches(0)
    setCurrentMatchIndex(-1)
    const idsProcessedInThisFullSearch = new Set<string>()
  
    // 创建匹配模式
    const matchPattern = createMatchPattern(searchText, {
      caseSensitive: options.caseSensitive,
      useRegex: options.useRegex
    })
    
    if (!matchPattern) {
      processedRowNodeIdsRef.current = idsProcessedInThisFullSearch
      return
    }

    // 获取有序的列ID列表 - 使用getAllColumns()以避免虚拟滚动的影响
    let orderedColIds: string[] = []
    try {
      const allColumns = columnApi.getAllColumns()
      if (allColumns && allColumns.length > 0) {
        orderedColIds = allColumns
          .map(col => col.getColId())
          .filter(colId => colId !== 'ag-Grid-AutoColumn') // 过滤掉ag-grid的内部列
      }
    } catch (error) {
      console.warn('获取列定义失败，将使用默认顺序:', error)
    }
  
    const initialMatches: MatchLocation[] = []
  
    api.forEachNode((rowNode: any) => {
      if (!rowNode?.data) {
        return
      }
      // 使用统一的匹配函数，传入有序的列ID
      
      const matchedColIds = performCellMatching(rowNode, matchPattern, orderedColIds?.length > 0 ? orderedColIds : undefined)
      // 为每个匹配的列创建 MatchLocation
      matchedColIds.forEach(colId => {
        initialMatches.push({ rowNode, colId })
      })
      
      if (rowNode.id) {
        idsProcessedInThisFullSearch.add(rowNode.id)
      }
    })
    
    processedRowNodeIdsRef.current = idsProcessedInThisFullSearch
    setAllMatchLocations(initialMatches)
    setTotalMatches(initialMatches.length)
    const newCurrentIndex = initialMatches.length > 0 ? 0 : -1
    setCurrentMatchIndex(newCurrentIndex)
    
    // 标记搜索已经执行过
    hasSearchExecutedRef.current = true
  
    if (newCurrentIndex !== -1 && initialMatches.length > 0) {
      const firstMatch = initialMatches[newCurrentIndex]
      if (firstMatch.rowNode && firstMatch.rowNode.rowIndex !== null && firstMatch.rowNode.rowIndex !== undefined) {
        api.ensureIndexVisible(firstMatch.rowNode.rowIndex, 'middle')
        api.ensureColumnVisible(firstMatch.colId)
        api.setFocusedCell(firstMatch.rowNode.rowIndex, firstMatch.colId)
      }
    }
  }, [gridApiRef, performCellMatching])

  // 轮询等待 grid 准备完毕的函数
  const pollUntilGridReady = useCallback((
    searchText: string,
    options: SearchOptions,
    maxAttempts: number = 25,
    intervalMs: number = 200
  ) => {
    let attempts = 0
    
    const poll = () => {
      if (!gridApiRef.current) {
        return
      }
      
      const { api } = gridApiRef.current
      
      // 检查 grid 前几个节点是否有真正的数据，避免全量遍历
      let hasValidData = false
      let totalChecked = 0
      let nodesWithData = 0
      const maxNodesToCheck = 5 // 只检查前5个节点
      
      try {
        api.forEachNode((rowNode: any) => {
          totalChecked++
          if (rowNode?.data) {
            nodesWithData++
            hasValidData = true
          }
          if (totalChecked >= maxNodesToCheck) {
            return false // 停止遍历
          }
        })
      } catch (e) {
        // 如果遍历出错，说明数据可能还未准备好
        hasValidData = false
      }
      
      // 检查条件：
      // 1. 没有节点（空结果集，也是有效状态）
      // 2. 或者检查的节点中至少有一半包含数据
      const isReady = totalChecked === 0 || (hasValidData && nodesWithData >= Math.ceil(totalChecked / 2))
      
      if (isReady) {
        // 数据加载完成，执行搜索
        executeSearchLogic(searchText, options)
        setPendingSearch(null) // 清除待处理状态
        return
      }
      
      attempts++
      if (attempts >= maxAttempts) {
        executeSearchLogic(searchText, options)
        setPendingSearch(null) // 清除待处理状态
        return
      }
      
      // 继续轮询
      setTimeout(poll, intervalMs)
    }
    
    poll()
  }, [gridApiRef, executeSearchLogic])

  // 监听 AG-Grid 的 modelUpdated 事件，在数据模型更新后触发轮询机制
  useEffect(() => {
    if (!gridApiRef.current || !pendingSearch) {
      return
    }

    const { api } = gridApiRef.current

    const handleModelUpdated = () => {
      if (!pendingSearch) {
        return
      }
      // 在 modelUpdated 事件触发后，启动轮询机制来验证 DOM 渲染状态
      pollUntilGridReady(pendingSearch.text, pendingSearch.options)
    }

    // 注册事件监听器
    api.addEventListener('modelUpdated', handleModelUpdated)

    // 清理函数
    return () => {
      api.removeEventListener('modelUpdated', handleModelUpdated)
    }
  }, [gridApiRef, pendingSearch, pollUntilGridReady])

  // 快速筛选
  const handleQuickFilter = useCallback((searchText: string) => {
    if (!gridApiRef.current) {
      return
    }
    const { api } = gridApiRef.current

    // 更新搜索文本引用
    currentSearchTextRef.current = searchText
    
    // 清空缓存并重新获取数据
    api.refreshInfiniteCache()
  }, [gridApiRef])

  // 主搜索函数：处理全量搜索
  const handleSearch = useCallback((searchTextFromSearchBox: string, optionsFromSearchBox: SearchOptions) => {
    setCurrentSearchText(searchTextFromSearchBox)
    currentSearchTextRef.current = searchTextFromSearchBox
    setCurrentSearchOptions(optionsFromSearchBox)
    currentSearchOptionsRef.current = optionsFromSearchBox
  
    // 检查是否需要全量更新
    if (!gridApiRef.current) {
      return
    }
    
    const { api } = gridApiRef.current
    
    // 设置待处理搜索状态，等待 modelUpdated 事件触发轮询
    setPendingSearch({ text: searchTextFromSearchBox, options: optionsFromSearchBox })
    
    // 如果需要全量更新，则清除缓存强制刷新
    if (needFullUpdateRef.current === true) {
      api.purgeInfiniteCache()
      hasSearchExecutedRef.current = false
      return
    }

    if (optionsFromSearchBox.useQuickFilter) {
      handleQuickFilter(searchTextFromSearchBox)
      return
    }

    // 对于非快速筛选的情况，如果数据已经存在，不会触发 modelUpdated 事件
    // 这种情况下直接启动轮询机制
    if (!optionsFromSearchBox.useQuickFilter && !needFullUpdateRef.current) {
      pollUntilGridReady(searchTextFromSearchBox, optionsFromSearchBox)
    }
  }, [gridApiRef, executeSearchLogic, handleQuickFilter, pollUntilGridReady])

  // 搜索导航
  const handleSearchNavigation = useCallback((direction: 'next' | 'prev') => {
    if (totalMatches === 0 || !gridApiRef.current) return

    let nextIndex = currentMatchIndex
    if (direction === 'next') {
      nextIndex = (currentMatchIndex + 1) % totalMatches
    } else {
      nextIndex = (currentMatchIndex - 1 + totalMatches) % totalMatches
    }
    setCurrentMatchIndex(nextIndex)

    const { api } = gridApiRef.current
    const matchToNavigate = allMatchLocations[nextIndex]
    if (matchToNavigate && matchToNavigate.rowNode) {
      api.ensureIndexVisible(matchToNavigate.rowNode.rowIndex, 'middle')
      api.ensureColumnVisible(matchToNavigate.colId)
      api.setFocusedCell(matchToNavigate.rowNode.rowIndex, matchToNavigate.colId)
    }
  }, [totalMatches, currentMatchIndex, allMatchLocations, gridApiRef])

  // 设置搜索框可见性
  const setSearchVisibleWrapper = useCallback((visible: boolean) => {
    setSearchVisible(visible)
    if (!visible) {
      // 清除搜索状态
      setAllMatchLocations([])
      setTotalMatches(0)
      setCurrentMatchIndex(-1)
      matchedCellsSetRef.current = new Set()
      currentFocusCellKeyRef.current = null
      // 重置搜索执行标志
      hasSearchExecutedRef.current = false
    }
  }, [])

  // 包装的 setNeedFullUpdate 函数，同时更新 state 和 ref
  const setNeedFullUpdateWrapper = useCallback((need: boolean) => {
    setNeedFullUpdate(need)
    needFullUpdateRef.current = need
  }, [])

  // 分页后重新触发搜索
  const retriggerSearchAfterPagination = useCallback(() => {
    // 如果当前有搜索文本，重新执行搜索
    if (currentSearchText.trim()) {
      setTimeout(() => {
        handleSearch(currentSearchText, currentSearchOptions)
      }, 1000)
    }
  }, [currentSearchText, currentSearchOptions, handleSearch])
  
  return {
    // 搜索状态
    searchVisible,
    currentSearchText,
    currentSearchOptions,
    allMatchLocations,
    currentMatchIndex,
    totalMatches,
    
    currentSearchTextRef,
    currentSearchOptionsRef,
    needFullUpdateRef,
    hasSearchExecutedRef,

    // 搜索操作
    setSearchVisible: setSearchVisibleWrapper,
    handleSearch,
    handleSearchNavigation,
    handleQuickFilter,
    
    // 高亮管理
    clearAllHighlights,
    addHighlightToCell,
    
    // 分页后重新搜索
    retriggerSearchAfterPagination,
    
    // 全量更新标识
    needFullUpdate,
    setNeedFullUpdate: setNeedFullUpdateWrapper,
    
    // 优化的高亮数据结构 (用于 cellClassRules)
    searchHighlightRefs: {
      matchedCellsSetRef,
      currentFocusCellKeyRef,
    }
  }
}
