// 路由菜单配置
import { FlowPage } from "src/pageTabs/flowPages/Flow";
import {
  AuditOverviewPage,
} from "src/pageTabs/audit";
import UserAudit from "src/pageTabs/audit/userPage";
import UserDetail from "src/pageTabs/audit/userPage/Detail";
import ObjectAudit from "src/pageTabs/audit/objectPage";
import PerimissionBoard from "src/pageTabs/audit/permissionPage";

import ConnectionManagementPage from "src/pageTabs/connectionManagementPage";
import { MyFolderPage } from "src/pageTabs/myFolderPage";
import { OrganizationPage } from "src/pageTabs/organizationPage";
import { QueryPage } from "src/pageTabs/queryPage";
import { SettingPage } from "src/pageTabs/settingPage";
import { SqlLogPage } from "src/pageTabs/sqlLogPage";
import { SysRolesPage } from "src/pageTabs/sysRolesPage";
import { UserPermsPage } from "src/pageTabs/userPermsPage";
import { UserMesPage } from "src/pageTabs/userMesPage";
import { UserParameterP } from "src/pageTabs/userParameter";
import AuthorizationManagePage from "src/pageTabs/authorizationManagePage";
import {
  HierarchicalAuthManagementPage,
  SubjectAuthorizationPage,
  BatchAuthorizationPage
} from 'src/pageTabs/database-management'
import AutomaticAuthorizationManagePage from "src/pageTabs/automaticAuthorizationManagePage";
import { RuleManagePage } from "src/pageTabs/ruleManagePage";
import { DataProtection } from "src/pageTabs/dataProtection";
import {
  TransferTaskRecord,
} from "src/pageTabs/dataTransfer";
import {
  MyApplicationPage,
  MyApprovalPage,
  WorkorderManagementPage,
} from "src/pageTabs/data-change";
import { IndexPage } from "src/pageTabs/indexPage"
import { PersonalCenterPage } from 'src/pageTabs/PersonalCenter'
import { MyApplyRequestPage, MyApplyApprovalPage, MyWorkOrderVisitPage } from 'src/pageTabs/access-request'
import { FlowDesginer } from 'src/pageTabs/flowPages/FlowDesginer'
import { FlowDesginer as DataChangeFlowDesginer } from 'src/pageTabs/data-change/flow-design'

// 自动授权对象集权限设置
import { DockerMonitor, HostMonitor } from "../pageTabs/sysMonitorPage";

// SQL批量执行
import SqlBatchExecution from "src/pageTabs/sqlBatchExecution";
import { TaskCenterPage } from "src/pageTabs/taskCenter/TaskCenterPage";
import {
  ToolConfigurationPage,
  ControlRecordPage
} from 'src/pageTabs/tool-control';
import HomeIndexPage from 'src/pageTabs/homePage'
import i18n from "i18next";
import DataClassificationPage from 'src/pageTabs/data-classification'

export const RouterMenus = [
  {
    key: "/system_index",
    title: i18n.t("routerMenus:home"), // "首页",
    component: IndexPage,
  },
  {
    key: "/system_data_operate",
    title: i18n.t("routerMenus:dataOperation"), // "数据操作",
    component: QueryPage,
  },
  {
    key: "/batch_execute",
    title: i18n.t("routerMenus:batchExecution"), // "批量执行",
    component: SqlBatchExecution,
  },
  {
    key: "/flow_apply", // 对应流程 好像已没用,待删除
    title: i18n.t("routerMenus:privilegeEscalationApplication"), // "提权申请",
    component: FlowPage,
  },
  {
    key: "/data_transform",
    title: i18n.t("routerMenus:staticDesensitization"), // "静态脱敏",
    component: TransferTaskRecord,
  },
  {
    key: "/transfer-task-record", // 数据传入进来的页面
    title: i18n.t("routerMenus:taskRecords"), //"任务记录",
    component: TransferTaskRecord,
  },
  // 数据变更
  {
    key: "/data_change_mine_apply",
    title: i18n.t("routerMenus:myApplications"), // "我的申请",
    component: MyApplicationPage,
  },
  {
    key: "/data_change_mine_approve",
    title: i18n.t("routerMenus:myApprovals"), // "我的审批",
    component: MyApprovalPage,
  },
  {
    key: "/data_change_flow_design",
    title: i18n.t("routerMenus:customFlowDesign"), // "自定义流程设计",
    component: DataChangeFlowDesginer,
  },
  {
    key: "/data_change_work_order_management",
    title: i18n.t("routerMenus:workOrderManagement"), // "工单管理",
    component: WorkorderManagementPage,
  },
  // 审计分析
  {
    key: "/audit_view",
    title: i18n.t("routerMenus:auditOverview"), //"审计概览",
    component: AuditOverviewPage,
  },
  {
    key: "/object_audit",
    title: i18n.t("routerMenus:objectAudit"), // "对象审计",
    component: ObjectAudit,
  },
  {
    key: "/suser_audit",
    title: i18n.t("routerMenus:userAudit"),// "用户审计",
    component: UserAudit,
  },
  {
    key: "/permission_dashboard",
    title: i18n.t("routerMenus:permissionDashboard"),// "权限看板",
    component: PerimissionBoard,
  },
  {
    key: "/suser_detail",
    title: i18n.t("routerMenus:userDetails"), // "用户详情",
    component: UserDetail,
  },
  // 数据保护
  {
    key: "/data_protect",
    title: i18n.t("routerMenus:dataProtection"), // "数据保护管理",
    component: DataProtection,
  },
  //监控
  {
    key: "/container_monitor",
    title: i18n.t("routerMenus:containerMonitoring"), // "容器监控",
    component: DockerMonitor,
  },
  {
    key: "/machine_monitor",
    title: i18n.t("routerMenus:hostMonitoring"), // "主机监控",
    component: HostMonitor,
  },
  // 数据库管理
  {
    key: "/connection_management",
    title: i18n.t("routerMenus:connectionManagement"), // "连接管理",
    component: ConnectionManagementPage,
  },
  {
    key: "/batch-authorization",
    title: i18n.t("routerMenus:batchAuthorization"), // "批量授权",
    component: BatchAuthorizationPage,
  },
  {
    key: "/auth_management",
    title: i18n.t("routerMenus:objectAuthorization"), //"客体授权",
    component: AuthorizationManagePage,
  },
  {
    key: "/subject-authorization",
    title: i18n.t("routerMenus:subjectAuthorization"), // "主体授权",
    component: SubjectAuthorizationPage,
  },
  {
    key: "/hierarchical_auth_management",
    title: i18n.t("routerMenus:hierarchicalAuthorizationManagement"), // "分级授权管理",
    component: HierarchicalAuthManagementPage,
  },
  {
    key: "/automatic_authorization",
    title: i18n.t("routerMenus:automaticAuthorization"),  // "自动授权",
    component: AutomaticAuthorizationManagePage,
  },
  {
    key: "/rule_management",
    title: i18n.t("routerMenus:ruleManagement"), // "规则管理",
    component: RuleManagePage,
  },
  // 系统管理
  {
    key: "/person_management",
    title: i18n.t("routerMenus:userManagement"), // "用户管理",
    component: OrganizationPage,
  },
  {
    key: "/organization/userParameter",
    title: i18n.t("routerMenus:userParameters"), // "用户参数",
    component: UserParameterP,
  },
  {
    key: "/role_management",
    title: i18n.t("routerMenus:roleManagement"),  // "角色管理",
    component: SysRolesPage,
  },
  {
    key: "/system_settings",
    title: i18n.t("routerMenus:systemSettings"), // "系统设置",
    component: SettingPage,
  },
  {
    key: "/my-folder",
    title: i18n.t("routerMenus:myFolder"), // "个人文件夹",
    component: MyFolderPage,
  },
  {
    key: "/download",
    title: i18n.t("routerMenus:taskCenter"),  // "任务中心",
    component: TaskCenterPage,
  },
  {
    key: "/sql-log",
    title: i18n.t("routerMenus:executionHistory"), //"执行历史",
    component: SqlLogPage,
  },
  {
    key: "/my-permissions", // 路由跳转页面
    title: i18n.t("routerMenus:myPermissions"), // "个人权限",
    component: UserPermsPage,
  },
  // 消息
  {
    key: "/mes-management",
    title: i18n.t("routerMenus:personalMessages"), // "个人消息",
    component: UserMesPage,
  },
  {
    key: "/personal-center",
    title: i18n.t("routerMenus:personalCenter"), // "个人中心",
    component: PersonalCenterPage,
  },
  // 流程
  {
    key: "/mine_apply",
    title: i18n.t("routerMenus:mineApply"), // "我的申请",
    component: MyApplyRequestPage
  },
  {
    key: "/mine_approve",
    title: i18n.t("routerMenus:mineApprove"), // "我的审批",
    component: MyApplyApprovalPage
  },
  {
    key: "/flow_work_order_management",
    title: i18n.t("routerMenus:orderManagement"),  // "工单管理",
    component: MyWorkOrderVisitPage
  },
  {
    key: "/flow_design",
    title: i18n.t("routerMenus:customFlowDesign"), // "自定义流程设计",
    component: FlowDesginer
  },
  {
    key: "/client_config",
    title: i18n.t("routerMenus:toolConfiguration"), // '工具配置',
    component: ToolConfigurationPage
  },
  {
    key: "/client_manager_records",
    title: i18n.t("routerMenus:controlRecords"), // '管控记录',
    component: ControlRecordPage
  },
  {
    key: "/system_home",
    title: i18n.t('home.title'), // '管控记录',
    component: HomeIndexPage
  },
  {
    key: "/data_classification",
    title: '数据分类分级', // '数据分级分类',
    component: DataClassificationPage
  },
];
